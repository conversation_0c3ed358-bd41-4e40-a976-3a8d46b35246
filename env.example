# ================================================================================
# Operator AI Python - 环境变量配置示例
# ================================================================================
# 复制此文件为 .env 并填入实际值
# cp env.example .env

# ================================================================================
# 🔑 必需配置 - 以下配置项必须设置才能正常运行
# ================================================================================

# OpenAI API配置 (必需)
OPENAI_API_KEY=your_openai_api_key_here

# MCP服务认证 (必需)  
MCP_COOKIE=your_mcp_cookie_here

# ================================================================================
# ⚙️ OpenAI配置 - API和模型相关设置
# ================================================================================

# API基础URL (默认: https://ark-cn-beijing.bytedance.net/api/v3)
OPENAI_API_BASE=https://ark-cn-beijing.bytedance.net/api/v3

# 模型名称 (默认: ep-20250529182814-2slqf)
OPENAI_MODEL_NAME=ep-20250529182814-2slqf

# 模型温度，控制输出随机性 (默认: 0.0, 范围: 0.0-2.0)
OPENAI_TEMPERATURE=0.0

# ================================================================================
# 📝 日志配置 - 日志级别和输出设置
# ================================================================================

# 日志级别 (默认: INFO, 可选: DEBUG|INFO|WARNING|ERROR|CRITICAL)
LOG_LEVEL=INFO

# 日志文件路径 (默认: ./logs/operator_ai.log)
LOG_FILE=./logs/operator_ai.log

# 日志格式 (默认值已经很好，通常不需要修改)
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ================================================================================
# 🎯 功能开关配置 - 启用/禁用特定功能
# ================================================================================

# 是否启用链路追踪 (默认: true)
FEATURE_ENABLE_TRACE=true

# ================================================================================
# 🏗️ PlanAgent配置 - 计划式Agent行为设置
# ================================================================================

# 最大计划重试次数 (默认: 3, 范围: 1-10)
PLAN_AGENT_MAX_PLAN_ITERATIONS=3

# 是否启用人工审核 (默认: true)
PLAN_AGENT_ENABLE_HUMAN_FEEDBACK=true
