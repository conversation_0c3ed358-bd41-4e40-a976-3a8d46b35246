

"""
SmartAgent

提供标准的ReAct模式智能对话功能，支持MCP工具调用和会话管理
"""
import logging
from typing import Dict, Any, AsyncGenerator, Union, Optional
from langchain_core.runnables import RunnableConfig, Runnable
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver

from src.mcp_client import get_mcp_factory
from src.llm import get_model_factory
from src.prompts import LIVESTREAM_ASSISTANT_SYSTEM_PROMPT
from src.memory import MemoryIntegration
from src.utils.tool_debugger import log_agent_tool_calls

logger = logging.getLogger(__name__)


class SmartAgent:
    """基于 LangGraph 的 ReAct 智能代理"""
    
    def __init__(self, llm, mcp_client, memory, checkpointer):
        self.llm = llm
        self.mcp_client = mcp_client
        self.memory = memory
        self.checkpointer = checkpointer
        
        self.tools = []
        self.agent = None
        
        logger.info("🤖 SmartAgent 创建完成")
    
    async def _ensure_initialized(self):
        """确保代理已初始化"""
        if self.agent is not None:
            return
        
        self.tools = []
        
        try:
            self.tools = await self.mcp_client.get_tools()
            logger.info(f"✅ 成功获取了 {len(self.tools)} 个工具")
        except Exception as e:
            logger.error(f"❌ 获取工具失败，使用无工具模式: {e}")
            self.tools = []
        self.agent = create_react_agent(
            model=self.llm,
            tools=self.tools,
            checkpointer=self.checkpointer,
            prompt=LIVESTREAM_ASSISTANT_SYSTEM_PROMPT
        )
        
        logger.info(f"✅ SmartAgent 初始化完成，工具数量: {len(self.tools)}")
    

    
    async def ainvoke(self, user_input: str, session_id: str) -> Dict[str, Any]:
        """异步执行对话"""
        await self._ensure_initialized()
        
        config = RunnableConfig()
        config.setdefault("configurable", {})["thread_id"] = session_id
        
        memories = []
        if self.memory:
            memories = self.memory.search_memories(user_input, session_id, limit=3)
            if memories:
                logger.debug(f"🧠 找到 {len(memories)} 条相关记忆")
        
        try:
            assert self.agent is not None, "代理未初始化"
            
            messages = [("human", user_input)]
            if memories:
                memory_context = "相关记忆：" + "; ".join([
                    mem.get('memory', str(mem)) for mem in memories[:2]
                ])
                messages.insert(0, ("system", memory_context))
            
            result = await self.agent.ainvoke(
                {"messages": messages},
                config=config
            )
            
            result_messages = result.get("messages", [])
            
            # 记录工具调用日志
            await log_agent_tool_calls(result)
            final_response = ""
            
            if result_messages and hasattr(result_messages[-1], 'content'):
                final_response = result_messages[-1].content
            
            if not final_response:
                final_response = "处理完成，但没有生成响应"
            
            if self.memory and final_response:
                self.memory.add_conversation(
                    user_input=user_input,
                    assistant_response=final_response,
                    user_id=session_id
                )
            
            return {
                "response": final_response,
                "messages": result_messages,
                "session_id": session_id,
                "success": True,
                "memories_used": len(memories) if memories else 0
            }
            
        except Exception as e:
            logger.error(f"❌ 代理执行失败: {e}")
            return {
                "response": f"处理请求时遇到问题: {str(e)}",
                "messages": [],
                "session_id": session_id,
                "success": False,
                "error": str(e)
            }
    
    async def astream(self, user_input: str, session_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """异步流式执行"""
        await self._ensure_initialized()
        
        # 直接使用传入的 session_id
        config = RunnableConfig()
        config.setdefault("configurable", {})["thread_id"] = session_id
        
        # 获取相关记忆
        memories = []
        if self.memory:
            memories = self.memory.search_memories(user_input, session_id, limit=3)
        
        try:
            # 确保代理已初始化
            assert self.agent is not None, "代理未初始化"
            
            # 构建消息
            messages = [("human", user_input)]
            if memories:
                memory_context = "相关记忆：" + "; ".join([
                    mem.get('memory', str(mem)) for mem in memories[:2]
                ])
                messages.insert(0, ("system", memory_context))
            
            async for chunk in self.agent.astream(
                {"messages": messages},
                config=config,
                stream_mode="values"
            ):
                yield {
                    "chunk": chunk,
                    "session_id": session_id,
                    "success": True
                }
                
        except Exception as e:
            logger.error(f"❌ 流式执行失败: {e}")
            yield {
                "chunk": {"error": str(e)},
                "session_id": session_id,
                "success": False,
                "error": str(e)
            }
    


