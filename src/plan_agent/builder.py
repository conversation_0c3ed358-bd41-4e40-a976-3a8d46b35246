"""
Plan Agent 构建器
"""
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver

from .types import PlanAgentState
from .nodes.query_rewrite import create_query_rewrite_node
from .nodes.planning import create_planner_node  
from .nodes.human_feedback import create_human_review_node
from .nodes.execution import create_executor_node
from .nodes.response_generator import create_quality_response_node


async def create_plan_agent_graph(llm, mcp_client, checkpointer=None, available_tools=None):
    """创建5节点Plan Agent图"""
    
    # 获取并绑定工具到LLM
    if available_tools is None:
        available_tools = await mcp_client.get_tools()
    
    # 绑定工具到LLM - 这样LLM就知道实际可用的工具了
    tool_bound_llm = llm.bind_tools(available_tools, tool_choice="none")
    
    builder = StateGraph(PlanAgentState)
    
    # 创建节点实例
    query_rewrite_node = create_query_rewrite_node(tool_bound_llm)  # 使用绑定了业务工具的LLM
    planner_node = create_planner_node(llm, mcp_client, available_tools)  # 使用原始LLM，内部会绑定业务工具+PlanningOutput工具
    human_review_node = create_human_review_node()
    executor_node = create_executor_node(mcp_client, available_tools)  # 传递工具避免重复获取
    quality_response_node = create_quality_response_node(tool_bound_llm)  # 使用绑定了业务工具的LLM
    
    # 添加节点
    builder.add_node("query_rewrite", query_rewrite_node)
    builder.add_node("planner", planner_node)
    builder.add_node("human_review", human_review_node)
    builder.add_node("executor", executor_node) 
    builder.add_node("quality_response", quality_response_node)
    
    # 设置入口点
    builder.add_edge(START, "query_rewrite")
    
    # 编译图
    return builder.compile(
        checkpointer=checkpointer,
        interrupt_before=["human_review"]
    )
