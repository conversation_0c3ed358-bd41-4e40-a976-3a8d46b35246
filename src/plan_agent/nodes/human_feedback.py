"""
👤 人工审核节点
"""
from typing import Dict, Any
from langgraph.types import Command, interrupt
from langchain_core.messages import HumanMessage

from ..types import PlanAgentState
from src.utils import logger
from src.utils.node_debugger import debug_node
from src.config.settings import get_settings
from src.utils.trace_logger import get_trace_logger

trace_logger = get_trace_logger()


def create_human_review_node():
    """创建人工审核节点"""
    @debug_node("人工审核节点")
    async def human_review_node(state: PlanAgentState) -> Command:
        settings = get_settings()
        
        # 检查是否启用人工审核功能
        if not settings.plan_agent.enable_human_feedback:
            logger.info("人工审核功能已禁用，直接跳转到执行器")
            return Command(goto="executor")
        
        current_plan = state.get("current_plan")
        if not current_plan:
            return Command(goto="quality_response")
        
        # 构建审核信息
        review_message = (
            f"请审核执行计划:\n"
            f"标题: {current_plan.title}\n"
            f"推理: {current_plan.reasoning}\n"
            f"步骤数: {len(current_plan.steps)}\n\n"
            f"请选择: approve(批准) / edit(修改) / reject(拒绝)"
        )
        
        # 使用LangGraph的interrupt机制
        human_response = interrupt(review_message)
        
        # 处理人工响应
        if human_response.get("action") == "approve":
            logger.info("计划已获得人工批准")
            return Command(goto="executor")
        
        elif human_response.get("action") == "edit":
            logger.info("计划需要修改")
            modifications = human_response.get("modifications", {})
            # 应用修改到计划
            modified_plan = apply_modifications(current_plan, modifications)
            return Command(
                update={"current_plan": modified_plan},
                goto="executor"
            )
        
        elif human_response.get("action") == "reject":
            logger.info("计划被拒绝，重新规划")
            return Command(
                update={"replanning_count": state.get("replanning_count", 0) + 1},
                goto="planner"
            )
        
        else:
            # 默认继续执行
            return Command(goto="executor")
    
    return human_review_node


def apply_modifications(plan, modifications: Dict[str, Any]):
    """应用人工修改到计划"""
    # 实现计划修改逻辑
    if "title" in modifications:
        plan.title = modifications["title"]
    
    if "steps" in modifications:
        # 修改步骤
        for step_idx, step_mods in modifications["steps"].items():
            if 0 <= int(step_idx) < len(plan.steps):
                step = plan.steps[int(step_idx)]
                if "params" in step_mods:
                    step.params.update(step_mods["params"])
                if "description" in step_mods:
                    step.description = step_mods["description"]
    
    return plan
