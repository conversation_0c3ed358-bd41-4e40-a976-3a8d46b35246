from langchain_core.messages import SystemMessage, HumanMessage
from langgraph.types import Command

from ..types import PlanAgentState, QueryRewriteOutput
from src.prompts.plan_system_prompt import QUERY_REWRITE_TEMPLATE
from src.prompts.query_rewrite_prompts import QUERY_REWRITE_SYSTEM_MESSAGE
from src.utils.node_debugger import debug_node


def get_query_rewrite_system_message() -> str:
    return QUERY_REWRITE_SYSTEM_MESSAGE


def create_query_rewrite_node(llm):
    @debug_node("查询改写节点")
    async def query_rewrite_node(state: PlanAgentState) -> Command:
        user_query = state.get("user_query", "")
        
        if not user_query.strip():
            return Command(
                update={"errors": ["用户查询为空"]},
                goto="quality_response"
            )
        
        structured_llm = llm.with_structured_output(QueryRewriteOutput)
        
        prompt_content = QUERY_REWRITE_TEMPLATE.render(original_question=user_query)
        
        query_rewrite_result = await structured_llm.ainvoke([
            SystemMessage(content=get_query_rewrite_system_message()),
            HumanMessage(content=prompt_content)
        ])
        
        return Command(
            update={
                "optimized_query": query_rewrite_result.rewritten_query,
                "query_intent": query_rewrite_result.intent_analysis
            },
            goto="planner"
        )
    
    return query_rewrite_node


