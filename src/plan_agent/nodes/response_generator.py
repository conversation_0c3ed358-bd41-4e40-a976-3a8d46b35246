"""
质量响应节点：执行评估 + 响应生成 + 重规划决策
"""
from langchain_core.messages import SystemMessage, HumanMessage
from langgraph.types import Command
from pydantic import BaseModel, Field

from ..types import PlanAgentState, QualityAssessment, QualityResponseOutput
from src.prompts.response_generator_prompts import (
    QUALITY_RESPONSE_SYSTEM_MESSAGE
)
from src.utils import logger
from src.utils.node_debugger import debug_node


# 使用 types.py 中的 QualityResponseOutput


def create_quality_response_node(llm):
    @debug_node("质量响应节点")
    async def quality_response_node(state: PlanAgentState) -> Command:
        user_query = state.get("user_query", "")
        current_plan = state.get("current_plan")
        tool_results = state.get("tool_results", [])
        replanning_count = state.get("replanning_count", 0)
        
        if not tool_results:
            return handle_no_results(user_query)
        
        # 构建消息
        messages = build_quality_evaluation_messages(
            user_query, current_plan, tool_results, replanning_count
        )
        
        # 和其他节点保持一致，直接使用structured output
        structured_llm = llm.with_structured_output(QualityResponseOutput)
        quality_result = await structured_llm.ainvoke(messages)
        
        quality_assessment = create_quality_assessment(quality_result)
        
        should_replan = determine_replanning_need(quality_result, replanning_count)
        
        if should_replan:
            return Command(
                update={
                    "quality_assessment": quality_assessment,
                    "replanning_count": replanning_count + 1
                },
                goto="planner"
            )
        else:
            return Command(
                update={"quality_assessment": quality_assessment},
                goto="__end__"
            )
    
    return quality_response_node


def build_quality_evaluation_messages(user_query, plan, tool_results, replanning_count):
    """构建质量评估消息"""
    messages = [
        SystemMessage(content=QUALITY_RESPONSE_SYSTEM_MESSAGE),
        HumanMessage(content=f"""用户查询: {user_query}

执行计划: {format_plan_for_evaluation(plan)}

重规划次数: {replanning_count}

工具执行结果:
{format_tool_results_for_evaluation(tool_results)}""")
    ]
    
    return messages


def format_tool_results_for_evaluation(tool_results):
    """简洁地格式化工具结果"""
    if not tool_results:
        return "无执行结果"
    
    formatted = []
    for i, result in enumerate(tool_results, 1):
        status = "成功" if result.success else "失败"
        formatted.append(f"{i}. {result.tool_name}: {status}")
        
        if result.success and result.result:
            # 简单截取，避免过长
            result_str = str(result.result)[:1000]
            if len(str(result.result)) > 1000:
                result_str += "...(已截取)"
            formatted.append(f"   结果: {result_str}")
        
        if result.error:
            formatted.append(f"   错误: {result.error}")
    
    return "\n".join(formatted)


def format_plan_for_evaluation(plan):
    """简洁地格式化计划信息"""
    if not plan:
        return "无计划"
    return f"{plan.title} ({len(plan.steps)}个步骤)"


def create_quality_assessment(quality_result: QualityResponseOutput) -> QualityAssessment:
    """转换质量结果到评估对象"""
    return QualityAssessment(
        execution_quality=max(1, int(quality_result.execution_quality * 10)),
        response_quality=max(1, int(quality_result.response_quality * 10)),
        task_completion=max(1, int(quality_result.task_completeness * 10)),
        overall_score=max(1, int((quality_result.execution_quality + quality_result.response_quality + quality_result.task_completeness) / 3 * 10)),
        improvement_suggestions=[quality_result.replanning_reason] if quality_result.replanning_reason else [],
        final_response=quality_result.final_response
    )


def determine_replanning_need(quality_result: QualityResponseOutput, current_count: int) -> bool:
    """判断是否需要重新规划"""
    if current_count >= 2:  # 最多重规划2次
        return False
    return quality_result.needs_replanning and quality_result.execution_quality < 0.6


def handle_no_results(user_query: str) -> Command:
    """处理无结果情况"""
    return Command(
        update={"quality_assessment": QualityAssessment(
            execution_quality=1,
            response_quality=3,
            task_completion=1,
            overall_score=2,
            improvement_suggestions=[f"无法处理查询：{user_query}"],
            final_response=f"抱歉，无法处理您的查询：{user_query}"
        )},
        goto="__end__"
    )


def create_fallback_response(user_query: str) -> QualityResponseOutput:
    """创建解析失败时的后备响应"""
    return QualityResponseOutput(
        execution_quality=0.5,
        response_quality=0.5,
        task_completeness=0.5,
        needs_replanning=False,
        replanning_reason="",
        final_response=f"已基于工具执行结果分析了您的查询：{user_query}"
    )


# get_quality_system_message 现在从 prompts.response_generator_prompts 导入为 QUALITY_RESPONSE_SYSTEM_MESSAGE 
