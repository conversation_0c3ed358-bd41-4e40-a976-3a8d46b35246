"""
智能Agent平台应用程序入口

此模块是应用程序的入口点，负责初始化和配置系统
"""
import os
import time
import uuid
from typing import Dict, Any, Optional

from src.utils import logger
from src.config.settings import get_settings
from src.utils.exceptions import (
    OperatorAIException, ConfigurationError, handle_common_errors_async,
    log_exception, ErrorContext
)

# 初始化日志系统
settings = get_settings()
logger.setup(
    level=settings.log.level.lower(),
    log_file=settings.log.file,
    log_format=settings.log.format
)

from src.agents import SmartAgent
from src.llm import get_model_factory
from src.mcp_client import get_mcp_factory
from src.memory import MemoryIntegration
from langgraph.checkpoint.memory import MemorySaver

class Application:
    """智能Agent平台应用程序"""
    
    def __init__(self):
        """初始化应用程序"""
        self.settings = get_settings()
        
        model_factory = get_model_factory()
        mcp_factory = get_mcp_factory()
        llm = model_factory.get_default_model()
        mcp_client = mcp_factory.create_client()
        checkpointer = MemorySaver()
        
        try:
            memory = MemoryIntegration()
            logger.info("🧠 记忆功能已启用")
        except Exception as e:
            logger.warning(f"⚠️ 记忆功能初始化失败: {e}")
            memory = None
        
        self.smart_agent = SmartAgent(
            llm=llm,
            mcp_client=mcp_client,
            memory=memory,
            checkpointer=checkpointer
        )
        
        logger.info("🎉 应用程序初始化完成")
        logger.info("✅ 基于LangGraph标准架构")
        logger.info("已启用完整调用链路追踪")
    
    @handle_common_errors_async
    async def process_request(self, user_input: str, session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        异步处理用户请求（带完整链路追踪）
        
        Args:
            user_input: 用户输入
            session_id: 会话ID
            
        Returns:
            处理结果
        """
        start_time = time.time()
        
        # 生成会话ID
        if not session_id:
            session_id = str(uuid.uuid4())[:8]
        
        logger.info(f"🚀 处理请求: {user_input}")
        logger.info(f"🆔 会话ID: {session_id}")
        
        with ErrorContext("用户请求处理"):
            # Agent处理（自动初始化）
            response = await self.smart_agent.ainvoke(
                user_input=user_input,
                session_id=session_id
            )
            
            processing_time = time.time() - start_time
            logger.info(f"✅ 处理完成 (耗时: {processing_time:.3f}秒)")
            
            # 添加处理时间信息
            response.update({
                "processing_time": processing_time
            })
            
            return response
    



def main():
    """主函数"""
    # 设置全局异常处理
    import sys
    sys.excepthook = log_exception
    
    try:
        app = Application()
        
        import asyncio
        
        async def process_user_input():
            print("🤖 智能Agent平台已启动")
            print("💡 输入问题开始对话，输入 'exit'、'quit' 或 '退出' 结束程序")
            print("📝 输入 'help' 查看可用命令")
            print("🔍 已启用基础日志记录")
            print("-" * 50)
            
            # 为整个会话创建一个持续的会话ID
            current_session_id = str(uuid.uuid4())[:8]
            print(f"🆔 当前会话ID: {current_session_id}")
            print("💡 提示: 使用 'new' 命令开始新会话")
            
            while True:
                try:
                    user_input = input("\n👤 您: ").strip()
                    
                    if not user_input:
                        continue
                    
                    # 退出命令
                    if user_input.lower() in ['exit', 'quit', '退出', 'q']:
                        print("👋 再见！")
                        break
                    
                    # 新会话命令
                    if user_input.lower() in ['new', '新会话', 'new session']:
                        # 生成新的会话ID
                        current_session_id = str(uuid.uuid4())[:8]
                        print(f"🆔 开始新会话: {current_session_id}")
                        continue
                    
                    # 帮助命令
                    if user_input.lower() in ['help', '帮助', 'h']:
                        print(f"""
📖 可用命令:
- help/帮助: 显示此帮助信息
- exit/quit/退出: 结束程序
- new/新会话: 开始新的对话会话
- 其他: 直接输入问题进行对话

🔄 会话管理:
- 当前会话ID: {current_session_id}
- 同一会话中的所有对话都会保持上下文连续性

🔍 日志功能:
- 基础日志记录
                        """)
                        continue
                    

                    

                    
                    # 处理用户请求 - 使用持续的会话ID
                    print("🤔 思考中...")
                    result = await app.process_request(user_input, session_id=current_session_id)
                    
                    if result.get("success"):
                        print(f"\n🤖 助手: {result.get('response', '没有生成回复')}")
                        print(f"⏱️ 耗时: {result.get('processing_time', 0):.2f}秒")
                        
                        messages = result.get('messages', [])
                        if messages:
                            print(f"💬 消息总数: {len(messages)}")
                    else:
                        print(f"\n❌ 处理失败: {result.get('error', '未知错误')}")
                
                except KeyboardInterrupt:
                    print("\n\n👋 程序已中断，再见！")
                    break
                except OperatorAIException as e:
                    logger.error(f"应用异常: {e}")
                    print(f"❌ 应用错误: {e.message}")
                    if e.details:
                        print(f"详情: {e.details}")
                except Exception as e:
                    logger.exception("主循环异常:")
                    print(f"❌ 发生错误: {e}")
        
        # 运行主循环
        asyncio.run(process_user_input())
        
    except KeyboardInterrupt:
        print("\n👋 程序已中断，再见！")
    except ConfigurationError as e:
        logger.error(f"配置错误: {e}")
        print(f"❌ 配置错误: {e.message}")
        print("请检查配置文件和环境变量")
    except OperatorAIException as e:
        logger.error(f"应用异常: {e}")
        print(f"❌ 启动失败: {e.message}")
    except Exception as e:
        logger.exception("应用程序异常:")
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
