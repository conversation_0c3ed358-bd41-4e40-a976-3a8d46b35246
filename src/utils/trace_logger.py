"""
基于LangGraph Trace的调用链路日志系统

提供完整的Agent执行链路追踪，包括：
1. 所有节点的输入输出
2. MCP工具调用详情
3. 执行时间和性能分析
4. 美观的链路可视化
"""
import time
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from dataclasses import dataclass, asdict, field
from enum import Enum
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, ToolMessage

from src.utils.logger import get_logger

logger = get_logger("trace")

class NodeType(Enum):
    """节点类型枚举"""
    START = "开始"
    AGENT_THINK = "Agent思考" 
    TOOL_CALL = "工具调用"
    TOOL_RESULT = "工具结果"
    AGENT_RESPONSE = "Agent回复"
    END = "结束"

@dataclass
class TraceNode:
    """链路追踪节点"""
    node_id: str
    node_type: NodeType
    name: str
    description: str
    input_data: Any
    output_data: Any
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    success: bool = True
    error: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def finish(self, output_data: Any = None, success: bool = True, error: str = None):
        """完成节点执行"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        if output_data is not None:
            self.output_data = output_data
        self.success = success
        self.error = error

@dataclass
class TraceSession:
    """追踪会话"""
    session_id: str
    user_input: str
    start_time: float
    nodes: List[TraceNode]
    end_time: Optional[float] = None
    total_duration: Optional[float] = None
    success: bool = True
    final_response: str = ""

class TraceLogger:
    """调用链路追踪器"""
    
    def __init__(self):
        self.active_sessions: Dict[str, TraceSession] = {}
        self.completed_sessions: List[TraceSession] = []
        self.max_history = 100  # 最多保存100个历史会话
    
    def start_session(self, session_id: str, user_input: str) -> TraceSession:
        """开始新的追踪会话"""
        # 检查是否已存在同一会话
        if session_id in self.active_sessions:
            # 如果会话已存在，在现有会话中添加新的轮次
            existing_session = self.active_sessions[session_id]
            
            # 创建新轮次的开始节点
            turn_number = len([n for n in existing_session.nodes if n.node_type == NodeType.START])
            start_node = TraceNode(
                node_id=f"{session_id}_turn_{turn_number}_start",
                node_type=NodeType.START,
                name=f"第{turn_number + 1}轮对话开始",
                description=f"用户第{turn_number + 1}轮输入处理开始",
                input_data={"user_input": user_input, "turn": turn_number + 1},
                output_data=None,
                start_time=time.time()
            )
            start_node.finish({"turn_initiated": True})
            existing_session.nodes.append(start_node)
            
            # 更新会话的最新用户输入
            existing_session.user_input = f"{existing_session.user_input} | {user_input}"
            
            self._print_turn_start(existing_session, turn_number + 1, user_input)
            return existing_session
        
        # 创建新会话
        session = TraceSession(
            session_id=session_id,
            user_input=user_input,
            start_time=time.time(),
            nodes=[]
        )
        
        self.active_sessions[session_id] = session
        
        # 创建开始节点
        start_node = TraceNode(
            node_id=f"{session_id}_start",
            node_type=NodeType.START,
            name="会话开始",
            description="用户输入处理开始",
            input_data={"user_input": user_input},
            output_data=None,
            start_time=time.time()
        )
        start_node.finish({"session_initiated": True})
        session.nodes.append(start_node)
        
        self._print_session_start(session)
        return session
    
    def add_node(self, session_id: str, node_type: NodeType, name: str, 
                 description: str, input_data: Any, metadata: Dict[str, Any] = None) -> str:
        """添加新节点"""
        if session_id not in self.active_sessions:
            return ""
        
        session = self.active_sessions[session_id]
        node_id = f"{session_id}_node_{len(session.nodes)}"
        
        node = TraceNode(
            node_id=node_id,
            node_type=node_type,
            name=name,
            description=description,
            input_data=input_data,
            output_data=None,
            start_time=time.time(),
            metadata=metadata or {}
        )
        
        session.nodes.append(node)
        self._print_node_start(node)
        
        return node_id
    
    def finish_node(self, session_id: str, node_id: str, output_data: Any = None, 
                   success: bool = True, error: str = None):
        """完成节点执行"""
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        
        # 找到对应的节点
        for node in session.nodes:
            if node.node_id == node_id:
                node.finish(output_data, success, error)
                self._print_node_end(node)
                break
    
    def finish_session(self, session_id: str, final_response: str = "", 
                      success: bool = True) -> Optional[TraceSession]:
        """完成追踪会话"""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        
        # 计算当前轮次
        turn_number = len([n for n in session.nodes if n.node_type == NodeType.START])
        
        # 创建当前轮次的结束节点
        end_node = TraceNode(
            node_id=f"{session_id}_turn_{turn_number}_end",
            node_type=NodeType.END,
            name=f"第{turn_number}轮对话结束",
            description=f"用户第{turn_number}轮请求处理完成",
            input_data={"final_response": final_response, "turn": turn_number},
            output_data={"success": success},
            start_time=time.time()
        )
        end_node.finish({"turn_completed": True})
        session.nodes.append(end_node)
        
        # 更新会话状态但不移动到已完成列表（保持活跃状态以便后续轮次）
        session.success = success
        session.final_response = final_response
        
        self._print_turn_summary(session, turn_number, final_response, success)
        return session
    
    def _print_session_start(self, session: TraceSession):
        """打印会话开始"""
        logger.info("🎯" + "="*80)
        logger.info(f"📝 [会话开始] ID: {session.session_id}")
        logger.info(f"👤 [用户输入] {session.user_input}")
        logger.info(f"⏰ [开始时间] {datetime.fromtimestamp(session.start_time).strftime('%H:%M:%S.%f')[:-3]}")
        logger.info("🎯" + "="*80)
    
    def _print_node_start(self, node: TraceNode):
        """打印节点开始"""
        icon = self._get_node_icon(node.node_type)
        logger.info(f"\n{icon} [{node.node_type.value}] {node.name}")
        logger.info(f"📝 [描述] {node.description}")
        
        # 格式化输入数据
        input_str = self._format_data(node.input_data)
        logger.info(f"📥 [输入] {input_str}")
        
        # 打印元数据
        if node.metadata:
            metadata_str = self._format_data(node.metadata)
            logger.info(f"📋 [元数据] {metadata_str}")
    
    def _print_node_end(self, node: TraceNode):
        """打印节点结束"""
        status_icon = "✅" if node.success else "❌"
        logger.info(f"{status_icon} [{node.node_type.value}] 完成 - {node.name}")
        logger.info(f"⏱️ [耗时] {node.duration:.3f}秒")
        
        if node.success:
            output_str = self._format_data(node.output_data)
            logger.info(f"📤 [输出] {output_str}")
        else:
            logger.error(f"💥 [错误] {node.error}")
        
        logger.info("-" * 60)
    
    def _print_turn_start(self, session: TraceSession, turn_number: int, user_input: str):
        """打印轮次开始"""
        logger.info("🔄" + "="*80)
        logger.info(f"📝 [第{turn_number}轮对话] 会话ID: {session.session_id}")
        logger.info(f"👤 [用户输入] {user_input}")
        logger.info(f"⏰ [开始时间] {datetime.fromtimestamp(time.time()).strftime('%H:%M:%S.%f')[:-3]}")
        logger.info("🔄" + "="*80)
    
    def _print_turn_summary(self, session: TraceSession, turn_number: int, final_response: str, success: bool):
        """打印轮次摘要"""
        # 计算当前轮次的节点
        turn_nodes = [n for n in session.nodes if f"turn_{turn_number}" in n.node_id]
        turn_duration = sum(n.duration or 0 for n in turn_nodes)
        
        logger.info("\n🎉" + "="*80)
        logger.info(f"📊 [第{turn_number}轮摘要] 会话ID: {session.session_id}")
        logger.info(f"⏱️ [轮次耗时] {turn_duration:.3f}秒")
        logger.info(f"🎯 [轮次节点] {len(turn_nodes)}个")
        logger.info(f"✅ [成功状态] {'成功' if success else '失败'}")
        logger.info(f"🤖 [最终回复] {final_response}")
        logger.info("🎉" + "="*80)
    
    def _print_session_summary(self, session: TraceSession):
        """打印会话摘要"""
        # 确保总耗时不为None
        total_duration = session.total_duration or (time.time() - session.start_time)
        
        logger.info("\n🎉" + "="*80)
        logger.info(f"📊 [会话摘要] ID: {session.session_id}")
        logger.info(f"⏱️ [总耗时] {total_duration:.3f}秒")
        logger.info(f"🎯 [节点数] {len(session.nodes)}个")
        logger.info(f"✅ [成功状态] {'成功' if session.success else '失败'}")
        
        # 统计各类型节点
        node_stats = {}
        for node in session.nodes:
            node_type = node.node_type.value
            if node_type not in node_stats:
                node_stats[node_type] = {"count": 0, "total_time": 0, "success": 0}
            node_stats[node_type]["count"] += 1
            if node.duration:
                node_stats[node_type]["total_time"] += node.duration
            if node.success:
                node_stats[node_type]["success"] += 1
        
        logger.info("📈 [节点统计]")
        for node_type, stats in node_stats.items():
            success_rate = stats["success"] / stats["count"] * 100 if stats["count"] > 0 else 0
            avg_time = stats["total_time"] / stats["count"] if stats["count"] > 0 else 0
            logger.info(f"  {node_type}: {stats['count']}个, 成功率{success_rate:.1f}%, 平均耗时{avg_time:.3f}秒")
        
        logger.info(f"🤖 [最终回复] {session.final_response}")
        logger.info("🎉" + "="*80)
    
    def _get_node_icon(self, node_type: NodeType) -> str:
        """获取节点图标"""
        icons = {
            NodeType.START: "🚀",
            NodeType.AGENT_THINK: "🧠", 
            NodeType.TOOL_CALL: "🔧",
            NodeType.TOOL_RESULT: "📋",
            NodeType.AGENT_RESPONSE: "🤖",
            NodeType.END: "🎯"
        }
        return icons.get(node_type, "📌")
    
    def _format_data(self, data: Any, max_length: int = None) -> str:
        """格式化数据为可读字符串"""
        if data is None:
            return "None"
        
        try:
            if isinstance(data, str):
                return data
            elif isinstance(data, dict):
                # 特殊处理工具执行详情输出
                if "execution_details" in data and isinstance(data["execution_details"], list):
                    # 提取基础信息
                    base_info = {k: v for k, v in data.items() if k != "execution_details"}
                    
                    # 格式化工具执行详情
                    execution_details = []
                    for detail in data["execution_details"]:
                        if isinstance(detail, dict):
                            tool_name = detail.get("tool_name", "unknown")
                            status = detail.get("status", "❓")
                            input_params = detail.get("input_params", {})
                            output = detail.get("output", "")
                            duration = detail.get("duration", 0)
                            
                            # 格式化输入参数
                            if input_params:
                                params_str = ", ".join([f"{k}={v}" for k, v in input_params.items()])
                                input_str = f"{tool_name}({params_str})"
                            else:
                                input_str = f"{tool_name}()"
                            
                            # 格式化输出（截断长内容）
                            if isinstance(output, str) and len(output) > 100:
                                output_str = output[:100] + "..."
                            else:
                                output_str = str(output)
                            
                            execution_details.append(
                                f"{status} 步骤{detail.get('step_index', '?')}: {input_str} → {output_str} ({duration:.3f}s)"
                            )
                    
                    if execution_details:
                        base_str = json.dumps(base_info, ensure_ascii=False)
                        details_str = " | ".join(execution_details)
                        return f"{base_str[:-1]}, \"工具执行详情\": \"{details_str}\"}}"
                
                # 特殊处理计划工具详情输入
                elif "planned_tools" in data and isinstance(data["planned_tools"], list):
                    # 提取基础信息
                    base_info = {k: v for k, v in data.items() if k != "planned_tools"}
                    
                    # 格式化计划工具详情
                    planned_details = []
                    for tool in data["planned_tools"]:
                        if isinstance(tool, dict):
                            tool_name = tool.get("tool_name", "unknown")
                            input_params = tool.get("input_params", {})
                            
                            # 格式化输入参数
                            if input_params:
                                params_str = ", ".join([f"{k}={v}" for k, v in input_params.items()])
                                planned_details.append(f"步骤{tool.get('step_index', '?')}: {tool_name}({params_str})")
                            else:
                                planned_details.append(f"步骤{tool.get('step_index', '?')}: {tool_name}()")
                    
                    if planned_details:
                        base_str = json.dumps(base_info, ensure_ascii=False)
                        details_str = " | ".join(planned_details)
                        return f"{base_str[:-1]}, \"计划执行\": \"{details_str}\"}}"
                
                # 特殊处理任务规划的steps输出
                elif "steps" in data and isinstance(data["steps"], list):
                    steps_info = []
                    for step in data["steps"]:
                        if isinstance(step, dict) and "tool" in step and "params" in step:
                            params_str = ", ".join([f"{k}={v}" for k, v in step["params"].items()]) if step["params"] else "无参数"
                            steps_info.append(f"步骤{step.get('step_index', '?')}: {step['tool']}({params_str})")
                    
                    if steps_info:
                        base_info = {k: v for k, v in data.items() if k != "steps"}
                        base_str = json.dumps(base_info, ensure_ascii=False)
                        steps_str = " | ".join(steps_info)
                        return f"{base_str[:-1]}, \"详细步骤\": \"{steps_str}\"}}"
                
                # 普通字典处理
                json_str = json.dumps(data, ensure_ascii=False, indent=None)
                return json_str
            elif isinstance(data, list):
                json_str = json.dumps(data, ensure_ascii=False, indent=None)
                return json_str
            elif isinstance(data, BaseMessage):
                content = getattr(data, 'content', str(data))
                return f"{type(data).__name__}: {str(content)}"
            else:
                return str(data)
        except Exception as e:
            return f"<格式化失败: {str(e)}>"
    
    def get_session_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话摘要"""
        # 先查找活跃会话
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
        else:
            # 查找已完成会话
            session = None
            for s in self.completed_sessions:
                if s.session_id == session_id:
                    session = s
                    break
            
            if not session:
                return None
        
        # 计算实际的总耗时（对于活跃会话，使用当前时间）
        if session.total_duration is not None:
            total_duration = session.total_duration
        else:
            # 对于活跃会话，计算从开始到现在的时间
            total_duration = time.time() - session.start_time
        
        return {
            "session_id": session.session_id,
            "user_input": session.user_input,
            "total_duration": total_duration,
            "node_count": len(session.nodes),
            "success": session.success,
            "final_response": session.final_response,
            "nodes": [
                {
                    "name": node.name,
                    "type": node.node_type.value,
                    "duration": node.duration or 0,
                    "success": node.success
                }
                for node in session.nodes
            ]
        }

    def close_session(self, session_id: str) -> Optional[TraceSession]:
        """关闭会话并移动到已完成列表"""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        session.end_time = time.time()
        session.total_duration = session.end_time - session.start_time
        
        # 计算总体统计
        total_turns = len([n for n in session.nodes if n.node_type == NodeType.START])
        
        # 创建会话结束节点
        final_end_node = TraceNode(
            node_id=f"{session_id}_session_end",
            node_type=NodeType.END,
            name="会话最终结束",
            description=f"完整会话结束，共{total_turns}轮对话",
            input_data={"total_turns": total_turns},
            output_data={"session_completed": True},
            start_time=time.time()
        )
        final_end_node.finish({"session_fully_completed": True})
        session.nodes.append(final_end_node)
        
        # 移动到已完成会话
        del self.active_sessions[session_id]
        self.completed_sessions.append(session)
        
        # 保持历史记录数量限制
        if len(self.completed_sessions) > self.max_history:
            self.completed_sessions = self.completed_sessions[-self.max_history:]
        
        self._print_session_summary(session)
        return session

# 全局追踪器实例
trace_logger = TraceLogger()

def get_trace_logger() -> TraceLogger:
    """获取全局追踪器实例"""
    return trace_logger 
