"""
统一异常处理模块

定义自定义异常类和错误处理装饰器
"""
import functools
import traceback
from typing import Any, Callable, Optional, Type, Union
from src.utils import logger


class OperatorAIException(Exception):
    """基础异常类"""
    
    def __init__(self, message: str, code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.code = code or self.__class__.__name__
        self.details = details or {}


class ConfigurationError(OperatorAIException):
    """配置错误"""
    pass


class ModelInitializationError(OperatorAIException):
    """模型初始化错误"""
    pass


class MCPClientError(OperatorAIException):
    """MCP客户端错误"""
    pass


class ToolExecutionError(OperatorAIException):
    """工具执行错误"""
    pass


class AgentProcessingError(OperatorAIException):
    """Agent处理错误"""
    pass


class ValidationError(OperatorAIException):
    """数据验证错误"""
    pass


class NetworkError(OperatorAIException):
    """网络连接错误"""
    pass


def safe_execute(
    fallback_value: Any = None,
    exceptions: tuple = (Exception,),
    log_error: bool = True
):
    """
    安全执行装饰器
    
    Args:
        fallback_value: 异常时返回的默认值
        exceptions: 要捕获的异常类型
        log_error: 是否记录错误日志
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except exceptions as e:
                if log_error:
                    logger.error(f"执行 {func.__name__} 时发生错误: {e}")
                    if hasattr(e, 'details'):
                        logger.error(f"错误详情: {e.details}")
                return fallback_value
        return wrapper
    return decorator


def safe_execute_async(
    fallback_value: Any = None,
    exceptions: tuple = (Exception,),
    log_error: bool = True
):
    """
    异步安全执行装饰器
    
    Args:
        fallback_value: 异常时返回的默认值
        exceptions: 要捕获的异常类型
        log_error: 是否记录错误日志
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except exceptions as e:
                if log_error:
                    logger.error(f"执行 {func.__name__} 时发生错误: {e}")
                    if hasattr(e, 'details'):
                        logger.error(f"错误详情: {e.details}")
                return fallback_value
        return wrapper
    return decorator


def validate_not_none(value: Any, name: str) -> Any:
    """
    验证值不为None
    
    Args:
        value: 要验证的值
        name: 参数名称
        
    Returns:
        验证后的值
        
    Raises:
        ValidationError: 值为None时
    """
    if value is None:
        raise ValidationError(f"{name} 不能为空", details={"parameter": name})
    return value


def validate_not_empty(value: Union[str, list, dict], name: str) -> Union[str, list, dict]:
    """
    验证值不为空
    
    Args:
        value: 要验证的值
        name: 参数名称
        
    Returns:
        验证后的值
        
    Raises:
        ValidationError: 值为空时
    """
    if not value:
        raise ValidationError(f"{name} 不能为空", details={"parameter": name, "value": value})
    return value


def log_exception(
    exc_type: Type[Exception], 
    exc_value: Exception, 
    exc_traceback
):
    """
    统一的异常日志记录
    
    Args:
        exc_type: 异常类型
        exc_value: 异常值
        exc_traceback: 异常堆栈
    """
    error_msg = f"未捕获的异常: {exc_type.__name__}: {exc_value}"
    
    # 如果是自定义异常，记录详细信息
    if isinstance(exc_value, OperatorAIException):
        logger.error(f"{error_msg}")
        logger.error(f"错误代码: {exc_value.code}")
        if exc_value.details:
            logger.error(f"错误详情: {exc_value.details}")
    else:
        logger.error(f"{error_msg}")
    
    # 记录堆栈跟踪
    logger.error("".join(traceback.format_exception(exc_type, exc_value, exc_traceback)))


def handle_common_errors(func: Callable) -> Callable:
    """
    通用错误处理装饰器
    
    将常见异常转换为自定义异常
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except (ConnectionError, TimeoutError) as e:
            raise NetworkError(f"网络连接失败: {e}", details={"original_error": str(e)})
        except (ValueError, TypeError) as e:
            raise ValidationError(f"数据验证失败: {e}", details={"original_error": str(e)})
        except Exception as e:
            # 如果已经是自定义异常，直接抛出
            if isinstance(e, OperatorAIException):
                raise
            # 其他异常包装为通用异常
            raise OperatorAIException(f"未知错误: {e}", details={"original_error": str(e), "type": type(e).__name__})
    return wrapper


def handle_common_errors_async(func: Callable) -> Callable:
    """
    异步通用错误处理装饰器
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except (ConnectionError, TimeoutError) as e:
            raise NetworkError(f"网络连接失败: {e}", details={"original_error": str(e)})
        except (ValueError, TypeError) as e:
            raise ValidationError(f"数据验证失败: {e}", details={"original_error": str(e)})
        except Exception as e:
            # 如果已经是自定义异常，直接抛出
            if isinstance(e, OperatorAIException):
                raise
            # 其他异常包装为通用异常
            raise OperatorAIException(f"未知错误: {e}", details={"original_error": str(e), "type": type(e).__name__})
    return wrapper


class ErrorContext:
    """错误上下文管理器"""
    
    def __init__(self, operation: str, reraise: bool = True):
        self.operation = operation
        self.reraise = reraise
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_value, traceback):
        if exc_type is not None:
            logger.error(f"执行 {self.operation} 时发生错误: {exc_value}")
            if self.reraise:
                return False  # 重新抛出异常
            return True  # 抑制异常 
