"""
日志管理模块，提供统一的日志记录功能

此模块提供了统一的日志记录接口，支持：
1. 不同级别的日志记录（DEBUG, INFO, WARNING, ERROR, CRITICAL）
2. 日志格式化和输出控制
3. 文件和控制台日志
"""
import logging
import os
import sys
from typing import Optional, Dict, Any

# 日志级别映射
LOG_LEVELS = {
    "debug": logging.DEBUG,
    "info": logging.INFO,
    "warning": logging.WARNING,
    "error": logging.ERROR,
    "critical": logging.CRITICAL
}

# 默认日志格式
DEFAULT_LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

class Logger:
    """统一的日志管理类"""
    
    _instances: Dict[str, 'Logger'] = {}
    
    @classmethod
    def get_logger(cls, name: str = "app") -> 'Logger':
        """
        获取或创建日志记录器实例
        
        Args:
            name: 日志记录器名称
            
        Returns:
            Logger实例
        """
        if name not in cls._instances:
            cls._instances[name] = Logger(name)
        return cls._instances[name]
    
    def __init__(self, name: str):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称
        """
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        self.initialized = False
    
    def setup(
        self,
        level: str = "info",
        log_file: Optional[str] = None,
        log_format: str = DEFAULT_LOG_FORMAT
    ):
        """
        设置日志记录器
        
        Args:
            level: 日志级别，可选值：debug, info, warning, error, critical
            log_file: 日志文件路径，如果为None则只输出到控制台
            log_format: 日志格式
        """
        if self.initialized:
            return
        
        # 设置日志级别
        self.logger.setLevel(LOG_LEVELS.get(level.lower(), logging.INFO))
        
        # 创建格式化器
        formatter = logging.Formatter(log_format)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 如果指定了日志文件，创建文件处理器
        if log_file:
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
                
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        
        self.initialized = True
    
    def debug(self, message: str, *args, **kwargs):
        """记录DEBUG级别日志"""
        self.logger.debug(message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        """记录INFO级别日志"""
        self.logger.info(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        """记录WARNING级别日志"""
        self.logger.warning(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        """记录ERROR级别日志"""
        self.logger.error(message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        """记录CRITICAL级别日志"""
        self.logger.critical(message, *args, **kwargs)
    
    def exception(self, message: str, *args, **kwargs):
        """记录异常信息"""
        self.logger.exception(message, *args, **kwargs)

# 创建默认日志记录器
logger = Logger.get_logger()

def get_logger(name: str = "app") -> Logger:
    """
    获取日志记录器实例
    
    Args:
        name: 日志记录器名称
        
    Returns:
        Logger实例
    """
    return Logger.get_logger(name)

def get_module_logger(name: str = None) -> Logger:
    """
    获取模块级日志记录器
    
    Args:
        name: 日志记录器名称，如果为None则自动获取调用模块名
        
    Returns:
        Logger实例
    """
    if name is None:
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')
    
    return Logger.get_logger(name)
