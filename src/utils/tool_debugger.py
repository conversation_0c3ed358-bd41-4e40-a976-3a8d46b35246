"""
工具调用调试模块

提供Agent工具调用的日志记录功能
"""
import json
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


async def log_agent_tool_calls(result: Dict[str, Any]) -> None:
    """记录Agent执行结果中的工具调用详情"""
    messages = result.get("messages", [])
    for msg in messages:
        if hasattr(msg, 'tool_calls') and msg.tool_calls:
            for tool_call in msg.tool_calls:
                logger.info("=" * 60)
                logger.info(f"🛠️ 工具调用: {tool_call.get('name', 'Unknown')}")
                logger.info("-" * 30)
                logger.info("📥 入参:")
                try:
                    args = tool_call.get('args', {})
                    formatted_args = json.dumps(args, indent=2, ensure_ascii=False)
                    logger.info(formatted_args)
                except Exception:
                    logger.info(str(tool_call.get('args', {})))
                logger.info("=" * 60)
        
        elif hasattr(msg, 'content') and hasattr(msg, 'tool_call_id'):
            logger.info("=" * 60)
            logger.info(f"🔧 工具结果: {getattr(msg, 'name', 'Unknown')}")
            logger.info("-" * 30)
            logger.info("📤 出参:")
            logger.info(str(msg.content))
            logger.info("=" * 60) 
