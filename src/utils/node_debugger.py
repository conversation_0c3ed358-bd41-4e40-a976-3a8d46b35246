"""
节点调试模块

提供简洁的节点输入输出日志记录功能
"""
import json
import logging
from enum import Enum
from functools import wraps
from typing import Any, Callable

logger = logging.getLogger(__name__)


def debug_node(node_name: str):
    """节点调试装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(state: Any, *args, **kwargs):
            # 记录输入
            logger.info("=" * 80)
            logger.info(f"🔵 节点开始: {node_name}")
            logger.info("-" * 40)
            logger.info("📥 输入状态:")
            _log_state(state)
            
            try:
                # 执行原函数
                result = await func(state, *args, **kwargs)
                
                # 记录输出
                logger.info("-" * 40)
                logger.info("📤 输出结果:")
                _log_result(result)
                logger.info(f"✅ 节点完成: {node_name}")
                logger.info("=" * 80)
                
                return result
                
            except Exception as e:
                logger.info("-" * 40)
                logger.info(f"❌ 节点失败: {node_name}")
                logger.info(f"错误: {e}")
                logger.info("=" * 80)
                raise
                
        return wrapper
    return decorator


def _log_state(state: Any) -> None:
    """记录状态信息"""
    if hasattr(state, 'get'):
        # 只记录关键字段，避免日志过长
        key_fields = ['user_query', 'replanning_count', 'processing_mode']
        state_info = {}
        
        for field in key_fields:
            value = state.get(field)
            if value is not None:
                # 处理枚举类型
                if isinstance(value, Enum):
                    state_info[field] = value.value
                else:
                    state_info[field] = value
        
        # 记录计划信息
        current_plan = state.get('current_plan')
        if current_plan:
            state_info['current_plan'] = {
                'title': getattr(current_plan, 'title', ''),
                'steps_count': len(getattr(current_plan, 'steps', [])),
                'needs_review': getattr(current_plan, 'needs_review', False)
            }
        
        # 记录工具结果数量
        tool_results = state.get('tool_results', [])
        if tool_results:
            state_info['tool_results_count'] = len(tool_results)
        
        try:
            logger.info(json.dumps(state_info, indent=2, ensure_ascii=False))
        except (TypeError, ValueError) as e:
            # 如果仍然无法序列化，使用str表示
            logger.info(f"状态信息 (无法JSON化): {state_info}")
    else:
        logger.info(str(state))


def _log_result(result: Any) -> None:
    """记录结果信息"""
    if hasattr(result, 'update') and hasattr(result, 'goto'):
        # LangGraph Command对象
        result_info = {
            'goto': getattr(result, 'goto', ''),
            'update_keys': list(getattr(result, 'update', {}).keys()) if getattr(result, 'update') else []
        }
        
        # 显示更新的详细内容
        update = getattr(result, 'update', {})
        if update:
            logger.info("📝 更新内容详情:")
            for key, value in update.items():
                if key == 'current_plan' and hasattr(value, 'title'):
                    # 详细记录计划信息
                    plan_info = {
                        'title': getattr(value, 'title', ''),
                        'reasoning': getattr(value, 'reasoning', ''),
                        'needs_review': getattr(value, 'needs_review', False),
                        'steps': []
                    }
                    
                    steps = getattr(value, 'steps', [])
                    for i, step in enumerate(steps):
                        step_info = {
                            'index': i + 1,
                            'title': getattr(step, 'title', ''),
                            'tool': getattr(step, 'tool', ''),
                            'params': getattr(step, 'params', {}),
                            'description': getattr(step, 'description', '')
                        }
                        plan_info['steps'].append(step_info)
                    
                    logger.info(f"  {key}:")
                    logger.info(json.dumps(plan_info, indent=4, ensure_ascii=False))
                else:
                    logger.info(f"  {key}: {value}")
        
        try:
            logger.info("📋 命令摘要:")
            logger.info(json.dumps(result_info, indent=2, ensure_ascii=False))
        except (TypeError, ValueError):
            logger.info(f"结果信息 (无法JSON化): {result_info}")
    else:
        logger.info(str(result)) 
