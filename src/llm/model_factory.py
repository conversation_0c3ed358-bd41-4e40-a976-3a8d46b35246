"""
大语言模型工厂模块

提供类型安全的模型创建和管理功能
"""
from typing import Dict, Any, Optional
from langchain import chat_models
from src.utils import logger
from src.config.settings import get_settings
from src.utils.exceptions import ModelInitializationError, handle_common_errors


class ModelFactory:
    """模型工厂类"""
    
    def __init__(self, settings=None):
        """
        初始化模型工厂
        
        Args:
            settings: 配置实例，为None时使用默认配置
        """
        self.settings = settings or get_settings()
        self._models_cache = {}
    
    @handle_common_errors
    def create_llm(self, config: Dict[str, Any] = None) -> Any:
        """
        创建语言模型实例
        
        Args:
            config: 模型配置参数，会覆盖默认配置
            
        Returns:
            初始化好的语言模型实例
            
        Raises:
            ModelInitializationError: 模型初始化失败时
        """
        # 合并配置
        openai_config = self.settings.openai
        
        model_name = config.get("model_name", openai_config.model_name) if config else openai_config.model_name
        base_url = config.get("base_url", openai_config.api_base) if config else openai_config.api_base
        api_key = config.get("api_key", openai_config.api_key) if config else openai_config.api_key
        temperature = config.get("temperature", openai_config.temperature) if config else openai_config.temperature
        
        # 验证必需配置
        if not api_key:
            raise ModelInitializationError(
                "API密钥未配置",
                details={"config_source": "environment or config"}
            )
        
        if not model_name:
            raise ModelInitializationError(
                "模型名称未配置",
                details={"available_config": ["model_name", "OPENAI_MODEL_NAME"]}
            )
        
        # 生成缓存键
        cache_key = f"{model_name}_{base_url}_{temperature}_{hash(api_key)}"
        
        # 检查缓存
        if cache_key in self._models_cache:
            logger.debug(f"从缓存返回模型实例: {model_name}")
            return self._models_cache[cache_key]
        
        try:
            logger.info(f"🤖 初始化模型: {model_name}")
            logger.debug(f"   API端点: {base_url}")
            logger.debug(f"   温度: {temperature}")
            
            # 使用init_chat_model初始化模型
            model = chat_models.init_chat_model(
                model_name,
                model_provider="openai",
                temperature=temperature,
                api_key=api_key,
                base_url=base_url
            )
            
            # 缓存模型实例
            self._models_cache[cache_key] = model
            
            logger.info(f"✅ 模型 {model_name} 初始化成功")
            return model
            
        except Exception as e:
            error_msg = f"模型 {model_name} 初始化失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            raise ModelInitializationError(
                error_msg,
                details={
                    "model_name": model_name,
                    "base_url": base_url,
                    "temperature": temperature,
                    "original_error": str(e)
                }
            ) from e
    
    def get_default_model(self) -> Any:
        """
        获取默认配置的模型实例
        
        Returns:
            默认配置的语言模型实例
        """
        return self.create_llm()
    
    def clear_cache(self):
        """清空模型缓存"""
        self._models_cache.clear()
        logger.info("🗑️ 模型缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            缓存统计信息
        """
        return {
            "cached_models": len(self._models_cache),
            "cache_keys": list(self._models_cache.keys())
        }


# 默认模型工厂实例
_default_factory: Optional[ModelFactory] = None


def get_model_factory() -> ModelFactory:
    """
    获取默认模型工厂实例（单例模式）
    
    Returns:
        ModelFactory实例
    """
    global _default_factory
    if _default_factory is None:
        _default_factory = ModelFactory()
    return _default_factory


def reset_model_factory():
    """重置模型工厂（用于测试）"""
    global _default_factory
    if _default_factory:
        _default_factory.clear_cache()
    _default_factory = None
