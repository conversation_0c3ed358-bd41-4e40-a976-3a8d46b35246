"""
公会名称提取工具

使用function calling标准方式提取公会名称
"""

from typing import List
from langchain_core.tools import tool


@tool
def report_faction_names(faction_names: List[str]) -> dict:
    """
    报告从用户输入中识别出的公会名称
    
    当分析用户输入后，使用此工具报告识别出的公会名称。
    
    Args:
        faction_names: 从用户输入中识别出的公会名称列表。
                      如果没有识别出任何公会名称，请传入空列表[]。
                      
    Examples:
        # 输入："查询小象公会的收入数据" 
        # 应该调用：report_faction_names(faction_names=["小象公会"])
        
        # 输入："对比熊猫公会和企鹅工会的主播数量"
        # 应该调用：report_faction_names(faction_names=["熊猫公会", "企鹅工会"])
        
        # 输入："无忧传媒top违规信息"
        # 应该调用：report_faction_names(faction_names=["无忧传媒"])
        
        # 输入："今天天气怎么样"
        # 应该调用：report_faction_names(faction_names=[])
    
    Returns:
        dict: 包含识别结果的字典
    """
    return {
        "status": "success",
        "faction_names": faction_names,
        "count": len(faction_names)
    } 
