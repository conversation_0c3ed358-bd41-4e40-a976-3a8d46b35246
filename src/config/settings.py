"""
配置管理模块

使用Pydantic Settings进行类型安全的配置管理
"""
import os
from typing import Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class OpenAIConfig(BaseSettings):
    """OpenAI API配置"""
    api_key: str = Field(..., description="OpenAI API密钥")
    api_base: str = Field(
        default="https://ark-cn-beijing.bytedance.net/api/v3",
        description="API基础URL"
    )
    model_name: str = Field(
        default="ep-20250529182814-2slqf",
        description="模型名称"
    )
    temperature: float = Field(
        default=0.0,
        ge=0.0,
        le=2.0,
        description="模型温度"
    )
    
    class Config:
        env_prefix = "OPENAI_"
        case_sensitive = False


class MCPConfig(BaseSettings):
    """MCP服务配置"""
    cookie: str = Field(default="", description="MCP服务认证cookie")
    
    class Config:
        env_prefix = "MCP_"
        case_sensitive = False


class LogConfig(BaseSettings):
    """日志配置"""
    level: str = Field(
        default="INFO",
        pattern="^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$",
        description="日志级别"
    )
    file: str = Field(
        default="./logs/operator_ai.log",
        description="日志文件路径"
    )
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    
    class Config:
        env_prefix = "LOG_"
        case_sensitive = False


class PlanAgentConfig(BaseSettings):
    """PlanAgent配置"""
    # 规划配置
    max_plan_iterations: int = Field(
        default=3,
        ge=1,
        le=10,
        description="最大计划重试次数"
    )
    
    # 执行配置
    enable_parallel_execution: bool = Field(
        default=True,
        description="是否启用并行工具执行"
    )
    tool_execution_timeout: int = Field(
        default=30,
        ge=5,
        le=300,
        description="单个工具执行超时时间(秒)"
    )
    
    # 质量评估阈值
    min_execution_quality: float = Field(
        default=0.6,
        ge=0.0,
        le=1.0,
        description="最低执行质量阈值"
    )
    min_task_completeness: float = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        description="最低任务完整度阈值"
    )
    
    # 人工审核配置
    enable_human_feedback: bool = Field(
        default=True,
        description="是否启用人工审核"
    )
    
    class Config:
        env_prefix = "PLAN_AGENT_"
        case_sensitive = False


class AppSettings(BaseSettings):
    """应用程序全局配置"""
    
    openai: OpenAIConfig = OpenAIConfig()
    mcp: MCPConfig = MCPConfig()
    log: LogConfig = LogConfig()
    plan_agent: PlanAgentConfig = PlanAgentConfig()
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保日志目录存在
        os.makedirs(os.path.dirname(self.log.file), exist_ok=True)
    
    @field_validator('openai')
    @classmethod
    def validate_openai_config(cls, v):
        if not v.api_key:
            raise ValueError("OpenAI API key is required")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
_settings: Optional[AppSettings] = None


def get_settings() -> AppSettings:
    """获取应用配置实例（单例模式）"""
    global _settings
    if _settings is None:
        _settings = AppSettings()
    return _settings
