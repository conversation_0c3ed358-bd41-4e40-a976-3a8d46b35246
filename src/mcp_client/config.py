"""
MCP服务配置模块

此模块定义了所有可用的MCP服务配置
"""
import os
from src.config.settings import get_settings

# 获取配置实例
settings = get_settings()
MCP_COOKIE = settings.mcp.cookie

# 验证MCP Cookie是否配置
if not MCP_COOKIE:
    import warnings
    warnings.warn(
        "MCP_COOKIE 环境变量未配置，MCP服务可能无法正常访问。"
        "请在 .env 文件中设置 MCP_COOKIE=your_cookie_value",
        UserWarning
    )

# MCP服务配置字典
# 直接使用 langchain_mcp_adapters.MultiServerMCPClient 的标准格式
MCP_SERVERS = {
    # 内部运营MCP服务
    # "ark-operation-mcp": {
    #     "url": "https://goornr28.mcp.bytedance.net/mcp",
    #     "transport": "streamable_http",
    #     "description": "内部运营MCP服务，提供直播数据和运营工具"
    # },
    # 直播MCP服务
    "ark-operator-webcast": {
        "url": "http://ark_operator.webcast-mcp.bytedance.net/sse",
        "transport": "sse",
        "headers": {
            "Cookie": MCP_COOKIE
        },
        "description": "直播MCP服务，提供直播数据和运营功能"
    }
} 
