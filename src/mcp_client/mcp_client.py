"""MCP客户端，用于与外部MCP服务交互

该模块直接使用 langchain_mcp_adapters 的官方 MCP 客户端实现。
"""
import logging
from typing import Dict, Any, List, Optional

from langchain_mcp_adapters.client import MultiServerMCPClient
from src.utils import logger
from .config import MCP_SERVERS

class MCPNotificationFilter(logging.Filter):
    """过滤MCP通知验证警告的日志过滤器"""
    
    def filter(self, record):
        # 过滤掉MCP通知验证相关的警告
        if record.levelno == logging.WARNING:
            message = record.getMessage()
            if "Failed to validate notification" in message or "ServerNotification" in message:
                return False
        return True

# 设置 MCP 相关日志级别，过滤通知验证警告
logging.getLogger("root").setLevel(logging.ERROR)
root_logger = logging.getLogger()
mcp_filter = MCPNotificationFilter()
root_logger.addFilter(mcp_filter)
logging.basicConfig(level=logging.INFO)


class MCPClient:
    """MCP客户端，直接使用 langchain_mcp_adapters 的官方实现"""
    
    def __init__(
            self,
            servers: Optional[Dict[str, Dict[str, Any]]] = None,
            server_name: Optional[str] = None,
            api_key: str = ""
        ):
        """初始化MCP客户端"""
        logger.info("🚀 初始化MCP客户端")
        
        self.api_key = api_key
        self.servers = servers or MCP_SERVERS
        self.active_server_name = server_name or (next(iter(self.servers)) if self.servers else None)
        
        logger.info(f"🌐 配置服务器: {list(self.servers.keys())}")
        
        try:
            self._client = MultiServerMCPClient(self.servers)
            logger.info("✅ MCP客户端初始化成功")
        except Exception as e:
            logger.error(f"❌ MCP客户端初始化失败: {e}")
            raise MCPClientError(f"MCP客户端初始化失败: {e}") from e
    
    async def get_tools(self, server_name: Optional[str] = None, max_retries: int = 3):
        """获取MCP工具，带重试机制"""
        logger.info(f"🔧 获取MCP工具 (服务器: {server_name or '所有'})")
        
        import asyncio
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    logger.info(f"🔧 尝试获取工具 (第 {attempt + 1}/{max_retries} 次)")
                
                tools = await self._client.get_tools(server_name=server_name)
                logger.info(f"✅ 获取到 {len(tools)} 个工具")
                return tools
                
            except Exception as e:
                logger.warning(f"⚠️ 第 {attempt + 1} 次获取失败: {e}")
                if attempt == max_retries - 1:
                    logger.error(f"❌ 重试 {max_retries} 次后仍失败")
                    raise MCPClientError(f"获取工具失败: {e}") from e
                else:
                    await asyncio.sleep(1)
    
    async def get_resources(self, server_name: str, uris: Optional[List[str]] = None):
        """获取MCP资源"""
        logger.info(f"📁 获取MCP资源 (服务器: {server_name})")
        
        try:
            result = await self._client.get_resources(server_name, uris=uris)
            logger.info(f"✅ 获取到 {len(result)} 个资源")
            return result
        except Exception as e:
            logger.error(f"❌ 获取MCP资源失败: {e}")
            raise MCPClientError(f"获取资源失败: {e}") from e
    
    @classmethod
    def get_available_servers(cls) -> Dict[str, Dict[str, Any]]:
        """获取可用的MCP服务列表"""
        return MCP_SERVERS.copy()
    
    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> 'MCPClient':
        """从配置创建MCP客户端"""
        # 直接提取 mcpServers 配置
        mcp_servers = config.get("mcpServers", {})
        return cls(servers=mcp_servers)

    async def invoke_tool(self, tool_name: str, parameters: Dict[str, Any] = None, server_name: Optional[str] = None):
        """调用MCP工具"""
        try:
            tools = await self.get_tools(server_name=server_name)
            
            target_tool = None
            for tool in tools:
                if tool.name == tool_name:
                    target_tool = tool
                    break
            
            if not target_tool:
                available_tools = [tool.name for tool in tools]
                return {
                    "error": f"未找到工具: {tool_name}",
                    "available_tools": available_tools
                }
            
            result = await target_tool.ainvoke(parameters or {})
            
            return {
                "success": True,
                "result": result,
                "tool_name": tool_name,
                "parameters": parameters
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name,
                "parameters": parameters
            }
    
    def invoke_tool_sync(self, tool_name: str, parameters: Dict[str, Any] = None, server_name: Optional[str] = None):
        """调用MCP工具（同步版本）"""
        import asyncio
        
        try:
            # 在同步环境中运行异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(
                self.invoke_tool(tool_name, parameters, server_name)
            )
            loop.close()
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name,
                "parameters": parameters
            }


class MCPClientError(Exception):
    """MCP客户端异常"""
    pass
