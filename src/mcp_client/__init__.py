"""
MCP (Model Context Protocol) 模块

提供MCP客户端的创建和管理功能，支持依赖注入和工厂模式
"""
from typing import Dict, Any, Optional
from src.mcp_client.mcp_client import MCPClient, MCP_SERVERS
from src.utils import logger
from src.utils.exceptions import MCPClientError, handle_common_errors


class MCPClientFactory:
    """MCP客户端工厂类"""
    
    def __init__(self):
        """初始化MCP客户端工厂"""
        self._clients_cache = {}
    
    @handle_common_errors
    def create_client(self, config: dict = None) -> MCPClient:
        """
        创建MCP客户端实例
        
        Args:
            config: 可选的配置字典
            
        Returns:
            MCPClient实例
            
        Raises:
            MCPClientError: 客户端创建失败时
        """
        # 生成缓存键
        cache_key = self._generate_cache_key(config)
        
        # 检查缓存
        if cache_key in self._clients_cache:
            logger.debug("返回缓存的MCP客户端实例")
            return self._clients_cache[cache_key]
        
        try:
            logger.info("创建新的MCP客户端实例")
            
            if config:
                logger.info(f"使用自定义配置: {list(config.keys())}")
                client = MCPClient.from_config(config)
            else:
                logger.info("使用默认配置")
                client = MCPClient()
            
            # 缓存客户端实例
            self._clients_cache[cache_key] = client
            
            logger.info("MCP客户端实例创建完成")
            return client
            
        except Exception as e:
            error_msg = f"MCP客户端创建失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            raise MCPClientError(
                error_msg,
                details={"config": config, "original_error": str(e)}
            ) from e
    
    def _generate_cache_key(self, config: dict = None) -> str:
        """生成缓存键"""
        if config is None:
            return "default"
        
        # 使用配置的哈希值作为缓存键
        import json
        try:
            config_str = json.dumps(config, sort_keys=True)
            return f"custom_{hash(config_str)}"
        except Exception:
            # 如果配置无法序列化，使用配置键列表
            return f"custom_{hash(tuple(sorted(config.keys())))}"
    
    def clear_cache(self):
        """清空客户端缓存"""
        self._clients_cache.clear()
        logger.info("🗑️ MCP客户端缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            缓存统计信息
        """
        return {
            "cached_clients": len(self._clients_cache),
            "cache_keys": list(self._clients_cache.keys())
        }


# 默认工厂实例
_default_factory: Optional[MCPClientFactory] = None


def get_mcp_factory() -> MCPClientFactory:
    """
    获取MCP客户端工厂实例（单例模式）
    
    Returns:
        MCPClientFactory实例
    """
    global _default_factory
    if _default_factory is None:
        _default_factory = MCPClientFactory()
    return _default_factory


def reset_mcp_factory():
    """重置MCP客户端工厂（用于测试）"""
    global _default_factory
    if _default_factory:
        _default_factory.clear_cache()
    _default_factory = None


def get_available_servers() -> Dict[str, Dict[str, Any]]:
    """
    获取可用的MCP服务列表
    
    Returns:
        服务器名称到配置的映射字典
    """
    return MCP_SERVERS.copy()


def get_server_config(server_name: str) -> Optional[dict]:
    """
    根据服务名称获取MCP服务配置
    
    Args:
        server_name: MCP服务名称
        
    Returns:
        MCP服务配置字典，如果不存在则返回None
    """
    return MCP_SERVERS.get(server_name)


def get_servers_info() -> dict:
    """
    获取所有MCP服务的描述信息
    
    Returns:
        包含服务名称和描述的字典
    """
    return {
        name: config.get("description", "无描述")
        for name, config in MCP_SERVERS.items()
    }


def validate_server_config(config: dict) -> bool:
    """
    验证服务器配置
    
    Args:
        config: 配置字典
        
    Returns:
        配置是否有效
    """
    if not isinstance(config, dict):
        return False
    
    # 检查必需的字段
    required_fields = ["mcpServers"]
    for field in required_fields:
        if field not in config:
            logger.warning(f"配置缺少必需字段: {field}")
            return False
    
    # 检查mcpServers是否为字典
    if not isinstance(config["mcpServers"], dict):
        logger.warning("mcpServers 必须是字典类型")
        return False
    
    return True


# 导出主要类和函数
__all__ = [
    'MCPClient',
    'MCPClientFactory',
    'MCP_SERVERS', 
    'get_mcp_factory',
    'reset_mcp_factory',
    'get_available_servers',
    'get_server_config',
    'get_servers_info',
    'validate_server_config'
] 
