"""
Prompts 包初始化文件

提供系统提示词常量
"""

from .system_prompt import LIVESTREAM_ASSISTANT_SYSTEM_PROMPT
from .memory_prompts import IMPORTANCE_CLASSIFICATION_PROMPT, SUMMARY_PROMPT
from .plan_system_prompt import (
    QUERY_REWRITE_TEMPLATE,
    TASK_PLANNING_TEMPLATE,
    RESPONSE_GENERATION_TEMPLATE,
    PLAN_AGENT_SYSTEM_ROLE,
    QUERY_REWRITE_SYSTEM_MESSAGE,
    PLANNING_SYSTEM_MESSAGE,
    RESPONSE_GENERATION_SYSTEM_MESSAGE,
    FALLBACK_SYSTEM_MESSAGE,
    PLAN_AGENT_ROLES
)
from .planning_prompts import (
    INITIAL_PLANNING_SYSTEM_MESSAGE,
    REPLANNING_SYSTEM_MESSAGE,
    INITIAL_PLANNING_TEMPLATE,
    REPLANNI<PERSON>_TEMPLATE,
    PREVIOUS_RESULTS_TEMPLATE,
    QUALITY_FEEDBACK_TEMPLATE,
    get_planning_system_message
)
from .query_rewrite_prompts import (
    QUERY_REWRITE_SYSTEM_MESSAGE as NEW_QUERY_REWRITE_SYSTEM_MESSAGE
)
from .response_generator_prompts import (
    QUALITY_RESPONSE_SYSTEM_MESSAGE
)

__all__ = [
    "LIVESTREAM_ASSISTANT_SYSTEM_PROMPT",
    "IMPORTANCE_CLASSIFICATION_PROMPT",
    "SUMMARY_PROMPT",
    # Legacy PlanAgent prompts (Jinja2 templates)
    "QUERY_REWRITE_TEMPLATE",
    "TASK_PLANNING_TEMPLATE",
    "RESPONSE_GENERATION_TEMPLATE",
    "PLAN_AGENT_SYSTEM_ROLE",
    # Legacy System messages
    "QUERY_REWRITE_SYSTEM_MESSAGE",
    "PLANNING_SYSTEM_MESSAGE",
    "RESPONSE_GENERATION_SYSTEM_MESSAGE",
    "FALLBACK_SYSTEM_MESSAGE",
    "PLAN_AGENT_ROLES",
    # Planning prompts
    "INITIAL_PLANNING_SYSTEM_MESSAGE",
    "REPLANNING_SYSTEM_MESSAGE", 
    "INITIAL_PLANNING_TEMPLATE",
    "REPLANNING_TEMPLATE",
    "PREVIOUS_RESULTS_TEMPLATE",
    "QUALITY_FEEDBACK_TEMPLATE",
    "get_planning_system_message",
    # Query rewrite prompts
    "NEW_QUERY_REWRITE_SYSTEM_MESSAGE",
    # Response generator prompts
    "QUALITY_RESPONSE_SYSTEM_MESSAGE"
] 
