
import os

# 🎛️ Mem0 配置
MEMORY_CONFIG = {
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "memory_collection",
            "path": "./chroma_db"
        }
    },
    "llm": {
        "provider": "openai",
        "config": {
            "model": os.getenv("OPENAI_MODEL", "gpt-3.5-turbo"),
            "temperature": 0.1,
        }
    },
    "embedder": {
        "provider": "openai",
        "config": {
            "model": os.getenv("OPENAI_EMBEDDING_MODEL", "text-embedding-ada-002"),
        }
    }
}

 
