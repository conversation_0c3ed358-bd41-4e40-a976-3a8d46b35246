"""
🧠 Mem0 记忆集成

专门负责语义记忆的存储和检索
"""

import logging
from typing import List, Dict, Any
from mem0 import Memory
from .config import MEMORY_CONFIG

logger = logging.getLogger(__name__)


class MemoryIntegration:
    """Mem0 记忆管理"""
    
    def __init__(self):
        try:
            self.memory = Memory.from_config(MEMORY_CONFIG)
        except Exception as e:
            logger.error(f"🚫 Mem0 初始化失败: {e}")
            raise
    
    def add_conversation(self, user_input: str, assistant_response: str, user_id: str):
        """💾 添加对话到记忆"""
        try:
            messages = [
                {"role": "user", "content": user_input},
                {"role": "assistant", "content": assistant_response}
            ]
            
            self.memory.add(messages=messages, user_id=user_id)
            logger.debug(f"💾 保存对话到记忆 - 用户: {user_id}")
            
        except Exception as e:
            logger.warning(f"⚠️ 保存对话失败: {e}")
    
    def search_memories(self, query: str, user_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """🔍 搜索相关记忆"""
        try:
            result = self.memory.search(query=query, user_id=user_id, limit=limit)
            
            if isinstance(result, list):
                return result
            elif isinstance(result, dict) and 'memories' in result:
                return result['memories']
            else:
                return []
                
        except Exception as e:
            logger.warning(f"⚠️ 搜索记忆失败: {e}")
            return []
    
    def get_all_memories(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """📚 获取用户所有记忆"""
        try:
            result = self.memory.get_all(user_id=user_id, limit=limit)
            
            if isinstance(result, list):
                return result
            elif isinstance(result, dict) and 'memories' in result:
                return result['memories']
            else:
                return []
                
        except Exception as e:
            logger.warning(f"⚠️ 获取记忆失败: {e}")
            return []
    
    def clear_memories(self, user_id: str):
        """🗑️ 清除用户记忆"""
        try:
            result = self.memory.get_all(user_id=user_id)
            
            if isinstance(result, list):
                memory_list = result
            elif isinstance(result, dict) and 'memories' in result:
                memory_list = result['memories']
            else:
                memory_list = []
            
            for memory in memory_list:
                if isinstance(memory, dict) and 'id' in memory:
                    self.memory.delete(memory_id=memory["id"])
            
            logger.info(f"🧹 清除了 {len(memory_list)} 条记忆 - 用户: {user_id}")
            
        except Exception as e:
            logger.warning(f"⚠️ 清除记忆失败: {e}")
    
    def add_single_message(self, content: str, user_id: str):
        """📝 添加单条消息"""
        try:
            self.memory.add(messages=content, user_id=user_id)
            logger.debug(f"📝 保存单条消息 - 用户: {user_id}")
            
        except Exception as e:
            logger.warning(f"⚠️ 保存消息失败: {e}")
