"""
计划式Agent应用程序

此模块专注于展示PlanAgent的功能：
- 复杂任务分解和结构化处理
- 工作流：Query Rewriting → Plan → Human Review → Execute → Aggregate → Evaluation
- 完整的调用链追踪
"""

import asyncio
import time
import uuid
from typing import Dict, Any, Optional

from src.config.settings import get_settings
from src.utils import logger
from src.plan_agent import PlanAgent
from src.llm import get_model_factory
from src.mcp_client import get_mcp_factory
from src.memory import MemoryIntegration
from langgraph.checkpoint.memory import MemorySaver

# 初始化日志系统
settings = get_settings()
logger.setup(
    level=settings.log.level.lower(),
    log_file=settings.log.file,
    log_format=settings.log.format
)

class PlanApplication:
    """PlanAgent应用程序"""
    
    def __init__(self):
        """初始化应用程序"""
        model_factory = get_model_factory()
        mcp_factory = get_mcp_factory()
        llm = model_factory.get_default_model()
        mcp_client = mcp_factory.create_client()
        checkpointer = MemorySaver()
        
        try:
            memory = MemoryIntegration()
            logger.info("🧠 记忆功能已启用")
        except Exception as e:
            logger.warning(f"⚠️ 记忆功能初始化失败: {e}")
            memory = None
        
        self.plan_agent = PlanAgent(
            llm=llm,
            mcp_client=mcp_client,
            memory=memory,
            checkpointer=checkpointer
        )
        
        logger.info("🎉 PlanAgent平台初始化完成")
        logger.info("🏗️ PlanAgent: 复杂任务分解和结构化处理")
        
        # 显示系统特性
        logger.info("✨ 系统特性:")
        logger.info("  📋 工作流: 查询改写→任务规划→人工审核→工具执行→聚合分析→质量评估")
        logger.info("  🔄 智能降级: 失败时自动降级到SmartAgent处理")
        logger.info("  📝 完整追踪: 详细的执行过程记录和调试信息")
        logger.info("🔍 已启用完整调用链路追踪")
    
    async def process_request(self, user_input: str, session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        处理用户请求
        
        Args:
            user_input: 用户输入
            session_id: 会话ID
            
        Returns:
            处理结果
        """
        start_time = time.time()
        
        # 生成会话ID
        if not session_id:
            session_id = str(uuid.uuid4())[:8]
        
        logger.info(f"🚀 处理请求: {user_input}")
        logger.info(f"🆔 会话ID: {session_id}")
        
        # 获取配置的最大重试次数
        settings = get_settings()
        max_plan_iterations = settings.plan_agent.max_plan_iterations
        
        logger.info(f"⚙️ 最大计划重试次数: {max_plan_iterations}")
        
        try:
            logger.info("🏗️ 使用PlanAgent处理...")
            
            # PlanAgent处理
            response = await self.plan_agent.invoke(
                user_input=user_input,
                session_id=session_id
            )
            
            processing_time = time.time() - start_time
            logger.info(f"✅ 处理完成 (耗时: {processing_time:.3f}秒)")
            
            # 添加额外信息
            response.update({
                "processing_time": processing_time
            })
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ 处理失败: {e}")
            
            return {
                "success": False,
                "error": str(e),
                "session_id": session_id,
                "processing_time": processing_time
            }
    
    async def handle_interrupt(self, interrupt_result: Dict[str, Any], user_response: str) -> Dict[str, Any]:
        """
        处理interrupt并恢复执行
        
        Args:
            interrupt_result: 包含interrupt信息的结果
            user_response: 用户的回复
            
        Returns:
            恢复执行的结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"🔄 恢复执行: {user_response}")
            
            config = interrupt_result.get("config")
            if not config:
                raise ValueError("未找到恢复配置信息")
            
            # 恢复执行
            response = await self.plan_agent.resume(
                user_response=user_response,
                config=config
            )
            
            processing_time = time.time() - start_time
            logger.info(f"✅ 恢复完成 (耗时: {processing_time:.3f}秒)")
            
            # 添加额外信息
            response.update({
                "processing_time": processing_time
            })
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ 恢复失败: {e}")
            
            return {
                "success": False,
                "error": str(e),
                "processing_time": processing_time
            }


def main():
    """主函数"""
    try:
        app = PlanApplication()
        
        import asyncio
        
        async def process_user_input():
            print("🏗️ PlanAgent应用程序已启动")
            print("📋 工作流: 查询改写→任务规划→人工审核→工具执行→聚合分析→质量评估")
            print("🔄 内置智能降级: 失败时自动降级到SmartAgent处理")
            print()
            print("📖 支持的命令:")
            print("  <问题>                    - 使用PlanAgent处理问题")
            print("  help                     - 显示此帮助信息")
            print("  quit/exit                - 退出程序")
            print()
            print("-" * 60)
            
            # 当前会话ID
            current_session_id = str(uuid.uuid4())[:8]
            print(f"🆔 当前会话ID: {current_session_id}")
            
            # 存储待恢复的interrupt状态
            pending_interrupt = None
            
            while True:
                try:
                    user_input = input("\n👤 您: ").strip()
                    
                    if not user_input:
                        continue
                    
                    # 退出命令
                    if user_input.lower() in ['exit', 'quit', '退出', 'q']:
                        print("👋 再见！")
                        break
                    
                    # 新会话命令
                    if user_input.lower() in ['new', '新会话', 'new session']:
                        current_session_id = str(uuid.uuid4())[:8]
                        pending_interrupt = None
                        print(f"🆔 开始新会话: {current_session_id}")
                        continue
                    
                    # 帮助命令
                    if user_input.lower() in ['help', '帮助', 'h']:
                        print("\n📖 使用说明:")
                        print("🏗️ PlanAgent: 基于工作流的结构化任务处理")
                        print("📋 工作流程: 查询改写→任务规划→人工审核→工具执行→聚合分析→质量评估")
                        print("💡 直接输入问题即可开始处理，'quit'退出")
                        continue
                    
                    # 处理用户请求
                    print("🤔 PlanAgent分析处理中...")
                    
                    if pending_interrupt:
                        # 恢复被中断的执行
                        result = await app.handle_interrupt(pending_interrupt, user_input)
                        pending_interrupt = None
                    else:
                        # 正常处理请求
                        result = await app.process_request(
                            user_input, 
                            session_id=current_session_id
                        )
                    
                    # 检查是否需要interrupt
                    if result.get("interrupted"):
                        print(f"\n⏸️ 需要人工输入: {result.get('interrupt_message', '请提供反馈')}")
                        pending_interrupt = result
                        continue
                    
                    # 显示结果
                    if result.get("success"):
                        print(f"\n🏗️ PlanAgent: {result.get('response', '抱歉，没有生成回复')}")
                        print(f"⏱️ 耗时: {result.get('processing_time', 0):.2f}秒")
                        print(f"🔧 处理模式: {result.get('processing_mode', 'N/A')}")
                        print(f"🛠️ 工具数量: {result.get('tool_count', 0)}")
                        print(f"🎯 质量评分: {result.get('quality_score', 0):.1f}")
                    else:
                        print(f"\n❌ 处理失败: {result.get('error', '未知错误')}")
                
                except KeyboardInterrupt:
                    print("\n\n👋 程序已中断，再见！")
                    break
                except Exception as e:
                    logger.exception("主循环异常:")
                    print(f"❌ 发生错误: {e}")
        
        # 运行主循环
        asyncio.run(process_user_input())
        
    except KeyboardInterrupt:
        print("\n👋 程序已中断，再见！")
    except Exception as e:
        logger.exception("应用程序异常:")
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main() 
