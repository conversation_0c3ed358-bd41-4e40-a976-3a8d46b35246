[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "operator-ai-python"
version = "0.1.0"
description = "智能Agent平台"
authors = [
    {name = "zhoulong<PERSON>", email = "<EMAIL>"},
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"

dependencies = [
    # 核心LangChain生态
    "langchain>=0.3.0",
    "langchain-openai>=0.3.0",
    "langchain-community>=0.3.0",
    "langchain-core>=0.3.0",
    "langgraph>=0.4.0",
    "langgraph-checkpoint>=2.0.0",
    "langgraph-prebuilt>=0.2.0",
    # MCP支持
    "langchain-mcp-adapters>=0.1.0",
    "mcp>=1.0.0",
    # Memory layer
    "mem0ai>=0.1.0",
    # LLM和API
    "openai>=1.0.0",
    # 数据处理和验证
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    # 网络请求
    "httpx>=0.25.0",
    "requests>=2.31.0",
    # 配置和环境
    "python-dotenv>=1.0.0",
    # 模板引擎
    "jinja2>=3.0.0",
]

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
]


[tool.hatch.build.targets.wheel]
packages = ["src"]
