#!/bin/bash

# 智能Agent平台一键初始化脚本
set -e

echo "🔧 初始化智能Agent平台..."

# 检查并安装uv
if ! command -v uv &> /dev/null; then
    echo "📦 安装uv包管理器..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    export PATH="$HOME/.cargo/bin:$PATH"
fi

# 创建虚拟环境并同步依赖
echo "📦 创建虚拟环境并同步依赖..."
uv venv .venv
uv sync

# 创建配置文件
if [ ! -f ".env" ]; then
    echo "📋 创建配置文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件设置API密钥"
fi

# 创建必要目录
mkdir -p data/vector_db logs

echo "✅ 初始化完成！"
echo ""
echo "📋 下一步："
echo "1. 编辑 .env 文件设置API密钥"
echo "2. 运行: uv run python -m src.app" 
