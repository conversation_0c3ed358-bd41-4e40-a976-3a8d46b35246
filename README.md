# Operator AI Python

🤖 基于LangGraph标准架构的智能Agent平台

[![Python](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://python.org)
[![LangGraph](https://img.shields.io/badge/LangGraph-0.4+-green.svg)](https://github.com/langchain-ai/langgraph)
[![MCP](https://img.shields.io/badge/MCP-1.0+-orange.svg)](https://modelcontextprotocol.io)

## 📋 项目简介

Operator AI Python是一个基于LangGraph的智能Agent系统，提供两种不同的Agent实现模式。项目专注于提供实用的AI助手服务，支持MCP工具调用和基于Mem0的智能记忆系统。

### ✨ 核心特性

- 🎯 **双Agent架构** - SmartAgent（ReAct模式）+ PlanAgent（6节点工作流）
- 🔧 **MCP工具集成** - 基于`langchain-mcp-adapters`的工具调用支持
- 🧠 **ReAct推理模式** - 标准的推理-行动循环，智能决策工具调用时机
- 🔍 **执行链路追踪** - 记录Agent执行过程和性能数据
- 💾 **智能记忆系统** - 基于Mem0的语义记忆，会话内完整上下文，跨会话重点信息
- 🚀 **异步处理支持** - 支持流式和批量处理模式

## 🏗️ 系统架构

### 项目结构

```
src/
├── agents/                 # 🤖 SmartAgent实现
│   ├── smart_agent.py     #     基于create_react_agent的智能Agent
│   └── __init__.py        #     Agent包导出
├── plan_agent/            # 🏗️ PlanAgent实现
│   ├── plan_agent.py      #     6节点工作流Agent
│   ├── builder.py         #     Agent图构建器
│   ├── types.py           #     状态和类型定义
│   ├── nodes/             #     工作流节点实现
│   └── __init__.py        #     PlanAgent包导出
├── mcp_client/            # 🔌 MCP工具客户端
│   ├── mcp_client.py      #     langchain-mcp-adapters封装
│   ├── config.py          #     MCP服务配置
│   └── __init__.py        #     MCP包导出
├── llm/                   # 🧠 语言模型管理
│   ├── model_factory.py   #     模型工厂和缓存管理
│   └── __init__.py        #     LLM包导出
├── utils/                 # 🛠️ 工具组件
│   ├── trace_logger.py    #     执行链路追踪器
│   ├── logger.py          #     日志系统
│   ├── exceptions.py      #     异常处理
│   └── __init__.py        #     工具包导出
├── config/                # ⚙️ 配置管理
│   ├── settings.py        #     Pydantic配置管理
│   └── __init__.py        #     配置包导出
├── prompts/               # 📝 提示词管理
│   └── __init__.py        #     提示词包导出
├── app.py                 # 🚀 SmartAgent应用入口
└── plan_app.py            # 🏗️ PlanAgent应用入口
```

### 双Agent架构流程

```mermaid
graph TD
    A[👤 用户输入] --> B{选择Agent模式}
    B -->|简单任务| C[🤖 SmartAgent]
    B -->|复杂任务| D[🏗️ PlanAgent]
    
    C --> E[⚡ ReAct推理循环]
    E --> F[🔧 MCP工具调用]
    F --> G[💬 生成回复]
    
    D --> H[📝 查询改写]
    H --> I[📋 任务规划]
    I --> J[👤 人工审核]
    J --> K[🔧 工具执行]
    K --> L[📊 结果聚合]
    L --> M[✅ 质量评估]
    M --> G
    
    G --> N[📋 链路追踪记录]
    N --> O[📤 返回结果]
```

## 🔧 核心组件

### SmartAgent - 标准ReAct模式

基于LangGraph的`create_react_agent`实现：

```python
# 特点
✅ 标准ReAct架构      # 推理-行动循环
✅ 工具调用集成       # 自动选择和调用MCP工具
✅ 智能记忆管理       # Mem0语义记忆系统
✅ 流式处理支持       # 异步流式响应
```

### PlanAgent - 结构化工作流

工作流处理复杂任务：

```python
# 完整工作流程
📝 查询改写 → 📋 任务规划 → 👤 人工审核 → 🔧 工具执行 → 📊 结果聚合 → ✅ 质量评估

# 特点  
✅ 复杂任务分解       # 自动将复杂任务分解为子任务
✅ 人工审核机制       # 关键计划需要人工确认
✅ 智能降级机制       # 失败时自动降级到SmartAgent
✅ 完整追踪支持       # 详细的执行过程记录
```

### MCP工具集成

基于`langchain-mcp-adapters`的工具支持：

```python
# 实际功能
🔧 配置化工具加载     # 根据配置文件加载指定的MCP工具
🌐 多协议支持         # 支持HTTP/SSE/stdio协议
⚡ 基础错误处理       # 工具失败时降级到无工具模式
📋 工具状态管理       # 简单的工具可用性检查
```

### 执行链路追踪

Agent执行过程监控：

```python
# 追踪功能
📊 节点级追踪         # 记录每个执行节点的输入输出
⏱️ 性能统计          # 统计执行时间和成功率
🎨 格式化日志         # 美观的追踪日志输出
🔄 会话管理          # 支持多会话追踪
```

## 🚀 快速开始

### 环境要求

- **Python**: 3.10+ (推荐 3.11+)
- **包管理器**: uv (强烈推荐)
- **系统**: macOS / Linux / Windows

### 一键安装

```bash
# 1️⃣ 克隆项目
git clone [仓库URL]
cd operator-ai-python

# 2️⃣ 自动化安装
./setup.sh

# 3️⃣ 配置环境
cp env.example .env
# 编辑 .env 文件，填入必要的API密钥

# 4️⃣ 启动应用
# SmartAgent模式
uv run python -m src.app

# PlanAgent模式  
uv run python -m src.plan_app
```

### 手动安装

```bash
# 安装uv包管理器
curl -LsSf https://astral.sh/uv/install.sh | sh

# 创建虚拟环境
uv venv

# 安装项目依赖
uv sync

# 配置环境变量
cp env.example .env

# 启动应用
uv run python -m src.app
```

## ⚙️ 环境配置

### 配置文件设置

复制并编辑环境配置文件：

```bash
cp env.example .env
```

> 💡 **完整配置说明**: `env.example` 文件包含了所有可用的配置项、默认值和详细注释。

### 核心配置项

#### 🔑 必需配置
```bash
# OpenAI API配置 (必需)
OPENAI_API_KEY=your_openai_api_key_here

# MCP服务认证 (必需)
MCP_COOKIE=your_mcp_cookie_here
```

#### ⚙️ 常用可选配置
```bash
# API端点和模型
OPENAI_API_BASE=https://ark-cn-beijing.bytedance.net/api/v3
OPENAI_MODEL_NAME=ep-20250529182814-2slqf
OPENAI_TEMPERATURE=0.0

# PlanAgent行为控制
PLAN_AGENT_MAX_PLAN_ITERATIONS=3        # 最大重试次数(1-10)
PLAN_AGENT_ENABLE_HUMAN_FEEDBACK=true   # 是否启用人工审核

# 功能开关
FEATURE_ENABLE_TRACE=true               # 链路追踪

# 日志设置
LOG_LEVEL=INFO                          # 日志级别
```

### 配置优先级

配置的加载优先级（从高到低）：
1. **系统环境变量** - `export VAR=value`
2. **.env文件** - 项目根目录的 `.env` 文件
3. **默认值** - 代码中定义的默认值

## 🛠️ 使用指南

### SmartAgent模式

适合日常对话和简单任务：

```bash
# 启动SmartAgent
uv run python -m src.app

# 支持的命令
help     # 显示帮助信息
stats    # 查看系统状态
history  # 查看会话历史
trace    # 查看执行追踪
new      # 开始新会话
```

### PlanAgent模式

适合复杂任务和结构化处理：

```bash
# 启动PlanAgent
uv run python -m src.plan_app

# 工作流特点
- 📝 查询改写：优化用户输入
- 📋 任务规划：生成详细执行计划  
- 👤 人工审核：复杂计划人工确认
- 🔧 工具执行：并行执行工具调用
- 📊 结果聚合：整合执行结果
- ✅ 质量评估：生成最终回答

# 环境变量配置PlanAgent行为
PLAN_AGENT_MAX_PLAN_ITERATIONS=5        # 设置最大重试次数为5次
PLAN_AGENT_ENABLE_HUMAN_FEEDBACK=false  # 禁用人工审核
uv run python -m src.plan_app
```

## 🔧 开发指南

### 技术栈

```toml
# 核心框架 - LangGraph生态
langchain = ">=0.3.0"
langchain-openai = ">=0.3.0"
langchain-community = ">=0.3.0" 
langchain-core = ">=0.3.0"
langgraph = ">=0.4.0"
langgraph-checkpoint = ">=2.0.0"
langgraph-prebuilt = ">=0.2.0"

# MCP工具支持
langchain-mcp-adapters = ">=0.1.0"
mcp = ">=1.0.0"

# 记忆系统
mem0ai = ">=0.1.0"

# 其他依赖
openai = ">=1.0.0"
pydantic = ">=2.0.0"
httpx = ">=0.25.0"
```

### 开发工具

项目使用 `uv` 作为包管理器，支持快速的依赖管理和虚拟环境。

## 📊 调用链路示例

系统提供详细的执行链路追踪，实时展示Agent的思考和执行过程：

```
🎯================================================================================
📝 [会话开始] ID: 1199985f
👤 [用户输入] 无忧传媒top违规信息
⏰ [开始时间] 18:33:01.183
🎯================================================================================

🧠 [Agent思考] 智能处理
📝 [描述] 分析和处理用户请求
📥 [输入] {"user_input": "无忧传媒top违规信息"}

🔧 [工具调用] 🔧 [工具调用] query_faction_id
📝 [描述] 通过公会名称查询公会ID
📥 [输入] {"raw_input": "{'query_string': '无忧传媒'}"}
📋 [元数据] {"run_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "tool_name": "query_faction_id", "tool_description": "通过公会名称查询公会ID", "tool_type": "MCP"}

📋 [工具结果] 工具结果 - query_faction_id
📝 [描述] 工具 query_faction_id 执行结果
📥 [输入] {"tool_name": "query_faction_id"}
✅ [工具调用] 完成 - 🔧 [工具调用] query_faction_id
⏱️ [耗时] 0.756秒
📤 [输出] {"result_preview": "找到无忧传媒，公会ID: 13"}
------------------------------------------------------------
✅ [工具结果] 完成 - 工具结果 - query_faction_id
⏱️ [耗时] 0.001秒
📤 [输出] 公会信息：{"faction_id":"13","faction_name":"无忧传媒","principal":"张瀚"}
------------------------------------------------------------

🔧 [工具调用] 🔧 [工具调用] get_faction_top_ban_reason
📝 [描述] 工具用途：查询公会的 TOP 违规原因及相关统计信息，结果按违规次数排序。
输入描述：公会 ID（faction_id）。 
输出描述：公会的违规情况列表，每条记录包含违规原因、违规次数、官方建议链接以及违规类型。
📥 [输入] {"raw_input": "{'faction_id': '13'}"}
📋 [元数据] {"tool_name": "get_faction_top_ban_reason", "tool_type": "MCP"}

📋 [工具结果] 工具结果 - get_faction_top_ban_reason
📝 [描述] 工具 get_faction_top_ban_reason 执行结果
📥 [输入] {"tool_name": "get_faction_top_ban_reason"}
✅ [工具调用] 完成 - 🔧 [工具调用] get_faction_top_ban_reason
⏱️ [耗时] 0.897秒
📤 [输出] {"result_preview": "获取到2条违规记录：涉色情低俗内容、涉性诱惑内容或动作"}
------------------------------------------------------------
✅ [工具结果] 完成 - 工具结果 - get_faction_top_ban_reason
⏱️ [耗时] 0.001秒
📤 [输出] 违规详情：[{"ban_reason":"涉色情低俗内容","count":"1","focus_ban_action_type_str":"UncivilizedSpeech"},{"ban_reason":"涉性诱惑内容或动作","count":"1","focus_ban_action_type_str":"VulgarSexRelated"}]
------------------------------------------------------------

🔧 [工具调用] 🔧 [工具调用] get_agent_artifact_instructions  
📝 [描述] 获取Agent生成专业报告和表格的指令模板
📥 [输入] {"type": "violation_report"}
📋 [元数据] {"tool_name": "get_agent_artifact_instructions", "tool_type": "MCP"}

📋 [工具结果] 工具结果 - get_agent_artifact_instructions
📝 [描述] 工具 get_agent_artifact_instructions 执行结果
📥 [输入] {"tool_name": "get_agent_artifact_instructions"}
✅ [工具调用] 完成 - 🔧 [工具调用] get_agent_artifact_instructions
⏱️ [耗时] 0.993秒
📤 [输出] {"result_preview": "获取NoahArtifactTable模板，用于生成专业违规统计表格"}
------------------------------------------------------------
✅ [工具结果] 完成 - 工具结果 - get_agent_artifact_instructions
⏱️ [耗时] 0.001秒
📤 [输出] 表格生成指令："使用NoahArtifactTable组件创建专业的违规统计表格，包含违规原因、次数、类型和建议链接"
------------------------------------------------------------

✅ [Agent思考] 完成 - 智能处理
⏱️ [耗时] 31.507秒
📤 [输出] {"response_preview": "以下是关于无忧传媒（公会ID：13）的TOP违规信息：\n\n```NoahArtifactTable..."}
------------------------------------------------------------

🤖 [Agent回复] 生成回复
📝 [描述] 生成最终回复给用户
📥 [输入] {"processing_result": "处理完成"}
📋 [元数据] {"response_length": 1353}
✅ [Agent回复] 完成 - 生成回复
⏱️ [耗时] 0.000秒
📤 [输出] {"final_response": "生成了包含NoahArtifactTable表格的专业违规分析报告，含详细建议和改进措施"}
------------------------------------------------------------

🎉================================================================================
📊 [会话摘要] ID: 1199985f
⏱️ [总耗时] 31.508秒
🎯 [节点数] 10个
✅ [成功状态] 成功
📈 [节点统计]
  开始: 1个, 成功率100.0%, 平均耗时0.000秒
  Agent思考: 1个, 成功率100.0%, 平均耗时31.507秒
  工具调用: 3个, 成功率100.0%, 平均耗时0.882秒
  工具结果: 3个, 成功率100.0%, 平均耗时0.001秒
  Agent回复: 1个, 成功率100.0%, 平均耗时0.000秒
  结束: 1个, 成功率100.0%, 平均耗时0.000秒
🤖 [最终回复] 以下是关于无忧传媒（公会ID：13）的TOP违规信息：

\`\`\`NoahArtifactTable
{
  artifactId: "table-1",
  columns: [
    {
      title: "违规原因",
      dataIndex: "ban_reason",
      type: "text"
    },
    {
      title: "违规次数", 
      dataIndex: "count",
      type: "number"
    },
    {
      title: "违规类型",
      dataIndex: "focus_ban_action_type_str", 
      type: "text"
    },
    {
      title: "官方建议链接",
      dataIndex: "advice_link",
      type: "link"
    }
  ],
  dataSource: [
    {
      key: "1",
      ban_reason: "涉色情低俗内容",
      count: 1,
      focus_ban_action_type_str: "不文明言论",
      advice_link: {
        title: "查看建议",
        url: "https://p3-webcast.douyinpic.com/img/webcast/image/7f3031b66afe47cfaa5bd65ef211e864.todo~tplv-obj.image"
      }
    },
    {
      key: "2", 
      ban_reason: "涉性诱惑内容或动作",
      count: 1,
      focus_ban_action_type_str: "低俗色情相关",
      advice_link: {
        title: "查看建议",
        url: "https://p3-webcast.douyinpic.com/img/webcast/image/3ef1ea317dd047a38105e64a98e7c683.todo~tplv-obj.image"
      }
    }
  ],
  title: "无忧传媒TOP违规信息"
}
\`\`\`

### 分析建议

1. **违规原因**：无忧传媒的主要违规集中在"涉色情低俗内容"和"涉性诱惑内容或动作"上，各发生1次。
2. **建议**：
   - 针对"涉色情低俗内容"，建议加强对主播内容的审核，避免使用敏感词汇或暗示性语言。
   - 针对"涉性诱惑内容或动作"，建议规范主播行为，避免做出可能被平台判定为违规的动作或表演。

如需进一步分析或具体改进措施，请随时告知！
🎉================================================================================
```

## 🏗️ PlanAgent 技术方案

### 概述

PlanAgent是基于LangGraph StateGraph实现的结构化计划代理，采用6节点工作流架构，专为复杂任务分解和结构化处理设计。

### 核心架构

#### 节点工作流

![PlanAgent架构图](https://lf3-static.bytednsdoc.com/obj/eden-cn/ohvu_zlp/ljhwZthlaukjlkulzlp/framework/20250603-113931.jpeg)

#### 节点功能

| 节点 | 功能描述 | 技术实现 |
|------|----------|----------|
| **Query Rewrite** | 优化用户输入的语义表达 | Function Calling + 语义理解 |
| **Plan** | 将复杂任务分解为可执行步骤 | 任务规划算法 + 工具匹配 |
| **Human Review** | 人工审核执行计划 | 交互式确认 + 计划修改 |
| **Execute** | 并行执行所有工具调用 | 并发执行 + 错误处理 |
| **Aggregate** | 整合多个工具的执行结果 | 数据融合 + 专业分析 |
| **Evaluate** | 评估结果质量并决定下一步 | 质量评估 + 决策逻辑 |

### 技术实现

```python
# 启动PlanAgent应用
uv run python -m src.plan_app
```

### 扩展

![PlanAgent扩展架构图](https://lf3-static.bytednsdoc.com/obj/eden-cn/ohvu_zlp/ljhwZthlaukjlkulzlp/framework/20250603-112055.jpeg)

基于6节点工作流的PlanAgent扩展架构，支持灵活的功能扩展：

当需要支持新的业务需求时，只需在四个关键扩展点进行配置：

- **📋 Plan节点**：在任务规划逻辑中新增业务场景识别和处理策略
- **👤 Human Review节点**：定制人工审核规则和交互流程
- **🔧 Execute节点**：开发新的MCP工具或本地工具，自动注册到工具库  
- **✅ 质量评估节点**：制定新业务场景的结果质量评估标准和验证规则

## 📊 PlanAgent调用链路示例

```
🎯================================================================================
📝 [会话开始] ID: a301d675
👤 [用户输入] 无忧传媒top违规信息
⏰ [开始时间] 11:52:02.423
🎯================================================================================

🧠 [Agent思考] 查询改写
📝 [描述] 优化用户输入的语义表达
📥 [输入] {"original_input": "无忧传媒top违规信息"}
✅ [Agent思考] 完成 - 查询改写
⏱️ [耗时] 3.186秒
📤 [输出] {"rewritten": "分析无忧传媒公会中违规情况最严重的主播或违规类型，明确违规数据的统计时间范围和具体违规类型。"}
------------------------------------------------------------

🧠 [Agent思考] 任务规划
📝 [描述] 分析问题并制定执行计划
📥 [输入] {"query": "分析无忧传媒公会中违规情况最严重的主播或违规类型，明确违规数据的统计时间范围和具体违规类型。"}
✅ [Agent思考] 完成 - 任务规划
⏱️ [耗时] 3.290秒
📤 [输出] {"step_count": 3, "mode": "structured", "详细步骤": "步骤1: query_faction_id(faction_name=无忧传媒) | 步骤2: get_faction_top_ban_reason(faction_name=无忧传媒) | 步骤3: repeat_ban_anchor_in_faction(faction_name=无忧传媒)"}
------------------------------------------------------------

🔧 [工具调用] 执行工具
📝 [描述] 按计划执行3个工具
📥 [输入] {"plan_steps": 3, "计划执行": "步骤1: query_faction_id(faction_name=无忧传媒) | 步骤2: get_faction_top_ban_reason(faction_name=无忧传媒) | 步骤3: repeat_ban_anchor_in_faction(faction_name=无忧传媒)"}

📋 [工具结果] 步骤1: query_faction_id
📝 [描述] 执行工具 query_faction_id
📥 [输入] {"faction_name": "无忧传媒"}
✅ [工具结果] 完成 - 步骤1: query_faction_id
⏱️ [耗时] 0.270秒
📤 [输出] {"message":"请求参数无效","status_code":20002}
------------------------------------------------------------

📋 [工具结果] 步骤2: get_faction_top_ban_reason
📝 [描述] 执行工具 get_faction_top_ban_reason
📥 [输入] {"faction_name": "无忧传媒"}
✅ [工具结果] 完成 - 步骤2: get_faction_top_ban_reason
⏱️ [耗时] 0.270秒
📤 [输出] {"message":"请求参数无效","status_code":20002}
------------------------------------------------------------

📋 [工具结果] 步骤3: repeat_ban_anchor_in_faction
📝 [描述] 执行工具 repeat_ban_anchor_in_faction
📥 [输入] {"faction_name": "无忧传媒"}
✅ [工具结果] 完成 - 步骤3: repeat_ban_anchor_in_faction
⏱️ [耗时] 0.270秒
📤 [输出] {"message":"服务器开小差了，请稍后重试","status_code":20010}
------------------------------------------------------------

✅ [工具调用] 完成 - 执行工具
⏱️ [耗时] 0.813秒
📤 [输出] {"total_tools": 3, "successful": 3, "failed": 0, "工具执行详情": "✅ 步骤1: query_faction_id(faction_name=无忧传媒) → {"message":"请求参数无效","status_code":20002} (0.270s) | ✅ 步骤2: get_faction_top_ban_reason(faction_name=无忧传媒) → {"message":"请求参数无效","status_code":20002} (0.270s) | ✅ 步骤3: repeat_ban_anchor_in_faction(faction_name=无忧传媒) → {"message":"服务器开小差了，请稍后重试","status_code":20010} (0.270s)"}
------------------------------------------------------------

🧠 [Agent思考] 聚合分析
📝 [描述] 整合工具结果生成回答
📥 [输入] {"tool_count": 3}
✅ [Agent思考] 完成 - 聚合分析
⏱️ [耗时] 22.553秒
📤 [输出] {"result_length": 933}
------------------------------------------------------------

🧠 [Agent思考] 质量评估
📝 [描述] 评估回答质量并决定下一步
📥 [输入] {"retry_count": 0}
✅ [Agent思考] 完成 - 质量评估
⏱️ [耗时] 0.000秒
📤 [输出] {"next_action": "finish", "is_satisfactory": true, "confidence": 0.9}
------------------------------------------------------------

🎉================================================================================
📊 [第1轮摘要] 会话ID: a301d675
⏱️ [轮次耗时] 30.418秒
🎯 [轮次节点] 5个
✅ [成功状态] 成功
🤖 [最终回复] ## 📊 **数据概况**

### 核心指标总结
1. **数据请求状态**：
   - 3个API请求均返回错误状态
   - 2个请求参数无效(20002)，1个服务器错误(20010)

2. **数据完整性**：
   - 当前无法获取任何有效业务数据
   - 关键指标（违规主播、违规类型、时间范围）全部缺失

## 🔍 **关键发现**

### 技术障碍分析
1. **参数问题**：
   - `query_faction_id`和`get_faction_top_ban_reason`存在参数校验失败
   - 可能原因：缺少必要参数/参数格式错误/权限不足

2. **服务稳定性**：
   - `repeat_ban_anchor_in_faction`出现服务器端错误
   - 建议检查服务健康状态和错误日志

## 💡 **业务建议**

### 立即行动
1. **技术排查**：
   - 检查API文档确认必填参数
   - 验证公会ID等关键参数有效性
   - 联系技术团队修复服务异常

2. **替代方案**：
   - 通过后台管理系统手动导出数据
   - 使用历史缓存数据临时分析

（建议优先解决status_code 20002的参数问题，该错误相对容易修复）

🎉================================================================================
```
