{
    // Python解释器路径
    "python.defaultInterpreterPath": "./.venv/bin/python",
    
    // Python分析路径
    "python.analysis.extraPaths": [
        "./src"
    ],
    
    // 自动激活虚拟环境
    "python.terminal.activateEnvironment": true,
    "python.terminal.activateEnvInCurrentTerminal": true,
    
    // 类型检查
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.autoImportCompletions": true,
    
    // 文件关联
    "files.associations": {
        "*.toml": "toml"
    },
    
    // 保存时自动格式化
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit"
    },
    
    // 排除文件
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".venv": false,
        "**/.pytest_cache": true,
        "**/.mypy_cache": true
    }
} 
