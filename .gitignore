# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.env
.venv

# IDE
.idea/
*.swp
*.swo
.DS_Store
.cursor
.claude

# Jupyter Notebook
.ipynb_checkpoints

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
.nox/

# Logs
*.log
logs/

# Local configuration
.env.local
.env.development.local
.env.test.local
.env.production.local

# DeepSeek model specific
model_cache/
.deepseek/

# RAG specific
vector_store/
embeddings/

# MCP specific
mcp_cache/

# UV specific
.uv/

# Modern Python tools
.mypy_cache/
.ruff_cache/
.coverage.*
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/_build/
site/

# OS specific
Thumbs.db
ehthumbs.db
Desktop.ini
